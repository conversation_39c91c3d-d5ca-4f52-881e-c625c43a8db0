@echo off
echo ========================================
echo    ATLAS Trading Assistant v2
echo    AI-Powered Trading System
echo ========================================
echo.

cd /d "%~dp0"

echo [1/4] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo [2/4] Checking dependencies...
python -c "import fastapi, uvicorn, pandas, numpy, requests, websockets, alpaca_trade_api, fmp_python" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
)

echo [3/4] Checking API credentials...
if not exist .env (
    echo ERROR: .env file not found! Please create one with your API keys.
    pause
    exit /b 1
)

echo [4/4] Starting ATLAS server...
echo.
echo Server will run at: http://localhost:8080
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the server
python atlas_web.py

if errorlevel 1 (
    echo ERROR: Server failed to start! Check the logs for details.
    pause
    exit /b 1
)

pause 