"""
Financial Modeling Prep (FMP) Integration
Comprehensive financial data and analysis for ATLAS
"""

import os
import requests
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class FMPIntegration:
    """Complete FMP API integration for all financial data"""
    
    def __init__(self):
        self.api_key = os.getenv("FMP_API_KEY")
        self.base_url = "https://financialmodelingprep.com/api"
        
        if not self.api_key:
            logger.warning("FMP API key not found. Some features will be limited.")
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Any]:
        """Make API request to FMP"""
        if not self.api_key:
            return None
            
        url = f"{self.base_url}/{endpoint}"
        params = params or {}
        params['apikey'] = self.api_key
        
        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"FMP API error: {e}")
            return None
    
    # ==================== INSIDER TRADING ====================
    
    def get_insider_trades(self, symbol: str = None, days: int = 30) -> List[Dict]:
        """Get real insider trading data"""
        endpoint = "v4/insider-trading"
        params = {"limit": 1000}
        
        if symbol:
            params["symbol"] = symbol
            
        data = self._make_request(endpoint, params)
        if not data:
            return []
            
        # Filter by date if specified
        if days > 0:
            cutoff_date = datetime.now() - timedelta(days=days)
            data = [trade for trade in data if 
                    datetime.strptime(trade.get('transactionDate', ''), '%Y-%m-%d') >= cutoff_date]
        
        # Enhance with analysis
        for trade in data:
            trade['netBuyValue'] = trade.get('value', 0) if trade.get('transactionType') == 'P-Purchase' else -trade.get('value', 0)
            trade['significance'] = self._calculate_insider_significance(trade)
            
        return data
    
    def get_insider_sentiment(self, symbol: str) -> Dict:
        """Analyze insider sentiment for a stock"""
        trades = self.get_insider_trades(symbol, days=90)
        
        if not trades:
            return {"sentiment": "neutral", "score": 0}
            
        total_buys = sum(1 for t in trades if 'Purchase' in t.get('transactionType', ''))
        total_sells = sum(1 for t in trades if 'Sale' in t.get('transactionType', ''))
        buy_value = sum(t.get('value', 0) for t in trades if 'Purchase' in t.get('transactionType', ''))
        sell_value = sum(t.get('value', 0) for t in trades if 'Sale' in t.get('transactionType', ''))
        
        sentiment_score = (buy_value - sell_value) / (buy_value + sell_value + 1)
        
        return {
            "sentiment": "bullish" if sentiment_score > 0.2 else "bearish" if sentiment_score < -0.2 else "neutral",
            "score": round(sentiment_score, 3),
            "total_buys": total_buys,
            "total_sells": total_sells,
            "buy_value": buy_value,
            "sell_value": sell_value,
            "recent_trades": trades[:10]
        }
    
    def _calculate_insider_significance(self, trade: Dict) -> str:
        """Calculate how significant an insider trade is"""
        value = trade.get('value', 0)
        
        if value > 10000000:  # $10M+
            return "very_high"
        elif value > 1000000:  # $1M+
            return "high"
        elif value > 100000:  # $100K+
            return "medium"
        else:
            return "low"
    
    # ==================== FUNDAMENTAL ANALYSIS ====================
    
    def get_financial_statements(self, symbol: str, period: str = "quarter", limit: int = 4) -> Dict:
        """Get comprehensive financial statements"""
        statements = {}
        
        # Income Statement
        endpoint = f"v3/income-statement/{symbol}"
        params = {"period": period, "limit": limit}
        statements['income'] = self._make_request(endpoint, params) or []
        
        # Balance Sheet
        endpoint = f"v3/balance-sheet-statement/{symbol}"
        statements['balance'] = self._make_request(endpoint, params) or []
        
        # Cash Flow
        endpoint = f"v3/cash-flow-statement/{symbol}"
        statements['cashflow'] = self._make_request(endpoint, params) or []
        
        return statements
    
    def get_key_metrics(self, symbol: str, period: str = "quarter", limit: int = 4) -> List[Dict]:
        """Get key financial metrics and ratios"""
        endpoint = f"v3/key-metrics/{symbol}"
        params = {"period": period, "limit": limit}
        return self._make_request(endpoint, params) or []
    
    def get_financial_ratios(self, symbol: str, period: str = "quarter", limit: int = 4) -> List[Dict]:
        """Get financial ratios"""
        endpoint = f"v3/ratios/{symbol}"
        params = {"period": period, "limit": limit}
        return self._make_request(endpoint, params) or []
    
    def get_company_profile(self, symbol: str) -> Dict:
        """Get detailed company profile"""
        endpoint = f"v3/profile/{symbol}"
        data = self._make_request(endpoint)
        return data[0] if data else {}
    
    def get_company_rating(self, symbol: str) -> Dict:
        """Get company rating and recommendation"""
        endpoint = f"v3/rating/{symbol}"
        data = self._make_request(endpoint)
        return data[0] if data else {}
    
    # ==================== ANALYST DATA ====================
    
    def get_analyst_estimates(self, symbol: str, period: str = "quarter", limit: int = 4) -> List[Dict]:
        """Get analyst estimates"""
        endpoint = f"v3/analyst-estimates/{symbol}"
        params = {"period": period, "limit": limit}
        return self._make_request(endpoint, params) or []
    
    def get_analyst_recommendations(self, symbol: str) -> List[Dict]:
        """Get analyst recommendations"""
        endpoint = f"v3/analyst-stock-recommendations/{symbol}"
        params = {"limit": 100}
        return self._make_request(endpoint, params) or []
    
    def get_price_targets(self, symbol: str) -> Dict:
        """Get analyst price targets"""
        endpoint = f"v4/price-target/{symbol}"
        data = self._make_request(endpoint) or []
        
        if not data:
            return {}
            
        # Calculate consensus
        targets = [t['priceTarget'] for t in data if t.get('priceTarget')]
        if targets:
            return {
                "average": sum(targets) / len(targets),
                "high": max(targets),
                "low": min(targets),
                "count": len(targets),
                "recent_targets": data[:5]
            }
        return {}
    
    # ==================== MARKET DATA ====================
    
    def get_quote(self, symbol: str) -> Dict:
        """Get real-time quote"""
        endpoint = f"v3/quote/{symbol}"
        data = self._make_request(endpoint)
        return data[0] if data else {}
    
    def get_historical_prices(self, symbol: str, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get historical price data"""
        endpoint = f"v3/historical-price-full/{symbol}"
        params = {}
        
        if from_date:
            params['from'] = from_date
        if to_date:
            params['to'] = to_date
            
        data = self._make_request(endpoint, params)
        return data.get('historical', []) if data else []
    
    def get_market_movers(self) -> Dict:
        """Get market movers (gainers, losers, most active)"""
        movers = {}
        
        # Gainers
        endpoint = "v3/stock_market/gainers"
        movers['gainers'] = self._make_request(endpoint) or []
        
        # Losers
        endpoint = "v3/stock_market/losers"
        movers['losers'] = self._make_request(endpoint) or []
        
        # Most Active
        endpoint = "v3/stock_market/actives"
        movers['actives'] = self._make_request(endpoint) or []
        
        return movers
    
    # ==================== ECONOMIC DATA ====================
    
    def get_economic_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get economic calendar events"""
        endpoint = "v3/economic_calendar"
        params = {}
        
        if from_date:
            params['from'] = from_date
        if to_date:
            params['to'] = to_date
            
        return self._make_request(endpoint, params) or []
    
    def get_sector_performance(self) -> List[Dict]:
        """Get sector performance"""
        endpoint = "v3/sectors-performance"
        return self._make_request(endpoint) or []
    
    # ==================== SCREENING & DISCOVERY ====================
    
    def screen_stocks(self, filters: Dict) -> List[Dict]:
        """Screen stocks based on criteria"""
        endpoint = "v3/stock-screener"
        
        # Convert filters to FMP format
        params = {
            "marketCapMoreThan": filters.get("min_market_cap", 0),
            "marketCapLowerThan": filters.get("max_market_cap", 10000000000000),
            "priceMoreThan": filters.get("min_price", 0),
            "priceLowerThan": filters.get("max_price", 100000),
            "volumeMoreThan": filters.get("min_volume", 0),
            "sector": filters.get("sector", ""),
            "industry": filters.get("industry", ""),
            "country": filters.get("country", "US"),
            "exchange": filters.get("exchange", ""),
            "limit": filters.get("limit", 100)
        }
        
        # Remove empty parameters
        params = {k: v for k, v in params.items() if v}
        
        return self._make_request(endpoint, params) or []
    
    def get_ipo_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get IPO calendar"""
        endpoint = "v3/ipo_calendar"
        params = {}
        
        if from_date:
            params['from'] = from_date
        if to_date:
            params['to'] = to_date
            
        return self._make_request(endpoint, params) or []
    
    def get_earnings_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get earnings calendar"""
        endpoint = "v3/earning_calendar"
        params = {}
        
        if from_date:
            params['from'] = from_date
        if to_date:
            params['to'] = to_date
            
        return self._make_request(endpoint, params) or []
    
    # ==================== TECHNICAL INDICATORS ====================
    
    def get_technical_indicators(self, symbol: str, indicator: str, interval: str = "daily", period: int = 10) -> List[Dict]:
        """Get technical indicators"""
        endpoint = f"v3/technical_indicator/{interval}/{symbol}"
        params = {
            "type": indicator,
            "period": period
        }
        return self._make_request(endpoint, params) or []
    
    # ==================== NEWS & SENTIMENT ====================
    
    def get_stock_news(self, symbol: str = None, limit: int = 50) -> List[Dict]:
        """Get stock news"""
        endpoint = "v3/stock_news"
        params = {"limit": limit}
        
        if symbol:
            params["tickers"] = symbol
            
        return self._make_request(endpoint, params) or []
    
    def get_press_releases(self, symbol: str, limit: int = 50) -> List[Dict]:
        """Get company press releases"""
        endpoint = f"v3/press-releases/{symbol}"
        params = {"limit": limit}
        return self._make_request(endpoint, params) or []
    
    # ==================== INSTITUTIONAL OWNERSHIP ====================
    
    def get_institutional_holders(self, symbol: str) -> List[Dict]:
        """Get institutional holders"""
        endpoint = f"v3/institutional-holder/{symbol}"
        return self._make_request(endpoint) or []
    
    def get_mutual_fund_holders(self, symbol: str) -> List[Dict]:
        """Get mutual fund holders"""
        endpoint = f"v3/mutual-fund-holder/{symbol}"
        return self._make_request(endpoint) or []
    
    def get_etf_holders(self, symbol: str) -> List[Dict]:
        """Get ETF holders"""
        endpoint = f"v3/etf-holder/{symbol}"
        return self._make_request(endpoint) or []
    
    # ==================== COMPREHENSIVE ANALYSIS ====================
    
    def get_full_analysis(self, symbol: str) -> Dict:
        """Get comprehensive analysis for a symbol"""
        analysis = {
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            "profile": self.get_company_profile(symbol),
            "quote": self.get_quote(symbol),
            "rating": self.get_company_rating(symbol),
            "insider_sentiment": self.get_insider_sentiment(symbol),
            "price_targets": self.get_price_targets(symbol),
            "key_metrics": self.get_key_metrics(symbol, limit=1),
            "financial_ratios": self.get_financial_ratios(symbol, limit=1),
            "institutional_ownership": {
                "institutions": self.get_institutional_holders(symbol)[:10],
                "mutual_funds": self.get_mutual_fund_holders(symbol)[:10],
                "etfs": self.get_etf_holders(symbol)[:10]
            },
            "recent_news": self.get_stock_news(symbol, limit=5)
        }
        
        return analysis


# Global instance
fmp = FMPIntegration() 