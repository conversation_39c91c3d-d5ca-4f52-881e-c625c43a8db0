"""
S&P 500 Symbol Lists for ATLAS Trading System
Updated list of S&P 500 companies for comprehensive market scanning
"""

import os
import asyncio
from api_bridge import call_alpaca_api
import pandas as pd

_symbol_cache = None

async def get_us_equity_symbols_from_alpaca():
    global _symbol_cache
    if _symbol_cache is not None:
        return _symbol_cache
    symbols = []
    try:
        page_token = None
        while True:
            params = {"status": "active", "asset_class": "us_equity"}
            if page_token:
                params["page_token"] = page_token
            result = await call_alpaca_api("assets", query_params=params)
            if not result["success"] or "data" not in result or not isinstance(result["data"], list):
                break
            assets = result["data"]
            for asset in assets:
                if asset.get("tradable") and asset.get("symbol") and "." not in asset["symbol"]:
                    symbols.append(asset["symbol"])
            # Alpaca pagination: break if less than 1000 returned
            if len(assets) < 1000:
                break
            page_token = assets[-1]["id"] if assets else None
    except Exception as e:
        print(f"Error fetching symbols from Alpaca: {e}")
        # Return fallback symbols if API fails
        symbols = SP500_SYMBOLS.copy()

    _symbol_cache = symbols
    return symbols

async def get_top_liquid_us_equities_from_alpaca(n=1000):
    try:
        # Get all tradable symbols
        symbols = await get_us_equity_symbols_from_alpaca()
        # For speed, sample only the first 1500 to avoid API overload
        symbols = symbols[:1500]
        from alpaca_integration import get_alpaca_account
        alpaca = get_alpaca_account()
        avg_volumes = []
        # Fetch average volume for each symbol (last 20 bars, 1Min)
        for symbol in symbols[:200]:  # Limit to first 200 to avoid rate limits
            try:
                df = await alpaca.aget_historical_bars(symbol, timeframe="1Min", limit=20)
                if df is not None and len(df) >= 10:
                    avg_vol = df['volume'].mean()
                    avg_volumes.append((symbol, avg_vol))
            except Exception:
                continue
        # Sort by average volume descending
        avg_volumes.sort(key=lambda x: x[1], reverse=True)
        top_symbols = [s for s, v in avg_volumes[:n]]
        return top_symbols
    except Exception as e:
        print(f"Error getting liquid symbols: {e}")
        # Return fallback list of popular symbols
        return OPTIONS_FOCUS_SYMBOLS + SP500_SYMBOLS[:50]

# Complete S&P 500 symbols (updated as of 2024)
SP500_SYMBOLS = [
    # Technology
    "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "NVDA", "TSLA", "META", "NFLX", "ADBE",
    "CRM", "ORCL", "CSCO", "INTC", "AMD", "NOW", "INTU", "IBM", "TXN", "QCOM",
    "AMAT", "LRCX", "ADI", "MRVL", "KLAC", "CDNS", "SNPS", "FTNT", "TEAM", "WDAY",
    "ZM", "DDOG", "SNOW", "NET", "OKTA", "CRWD", "ZS", "PANW", "CYBR", "SPLK",
    
    # Healthcare & Pharmaceuticals
    "JNJ", "PFE", "UNH", "ABBV", "MRK", "TMO", "LLY", "DHR", "ISRG", "GILD",
    "AMGN", "VRTX", "REGN", "BMY", "CVS", "CI", "HUM", "ANTM", "SYK", "BSX",
    "MDT", "EW", "IDXX", "IQV", "MTD", "DGX", "BIO", "WAT", "PKI", "TECH",
    
    # Financial Services
    "BRK-B", "JPM", "BAC", "WFC", "GS", "MS", "C", "AXP", "BLK", "SPGI",
    "CME", "ICE", "MCO", "COF", "USB", "TFC", "PNC", "SCHW", "CB", "MMC",
    "AON", "AJG", "PGR", "TRV", "ALL", "MET", "PRU", "AFL", "AIG", "HIG",
    
    # Consumer Discretionary
    "AMZN", "TSLA", "HD", "MCD", "NKE", "SBUX", "LOW", "TJX", "BKNG", "GM",
    "F", "MAR", "HLT", "MGM", "WYNN", "LVS", "CZR", "RCL", "CCL", "NCLH",
    "DIS", "CMCSA", "CHTR", "T", "VZ", "TMUS", "DISH", "SIRI", "FOXA", "FOX",
    
    # Consumer Staples
    "WMT", "PG", "KO", "PEP", "COST", "WBA", "CVS", "KR", "SYY", "ADM",
    "GIS", "K", "CPB", "CAG", "HRL", "TSN", "TAP", "STZ", "MO", "PM",
    "CL", "CHD", "CLX", "KMB", "HSY", "MDLZ", "MNST", "KDP", "SJM", "LW",
    
    # Energy
    "XOM", "CVX", "COP", "EOG", "SLB", "PSX", "VLO", "MPC", "PXD", "BKR",
    "HAL", "DVN", "FANG", "APA", "OXY", "KMI", "WMB", "OKE", "LNG", "EPD",
    
    # Industrials
    "BA", "CAT", "HON", "UNP", "LMT", "RTX", "DE", "UPS", "FDX", "WM",
    "GE", "MMM", "ITW", "EMR", "ETN", "PH", "CARR", "OTIS", "LHX", "GD",
    "NOC", "TDG", "CTAS", "FAST", "PAYX", "ADP", "VRSK", "IEX", "FTV", "XYL",
    
    # Materials
    "LIN", "APD", "SHW", "FCX", "NUE", "DOW", "DD", "PPG", "ECL", "NEM",
    "CTVA", "VMC", "MLM", "PKG", "IP", "CF", "MOS", "FMC", "ALB", "CE",
    
    # Real Estate
    "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "AVB", "EQR", "WY", "BXP",
    "VTR", "ARE", "DLR", "SPG", "O", "WELL", "ESS", "MAA", "UDR", "CPT",
    
    # Utilities
    "NEE", "SO", "DUK", "AEP", "EXC", "XEL", "PEG", "WEC", "ED", "ETR",
    "ES", "FE", "CNP", "NI", "LNT", "EVRG", "AEE", "CMS", "DTE", "PPL",
    
    # Communication Services
    "GOOGL", "META", "NFLX", "DIS", "CMCSA", "T", "VZ", "CHTR", "TMUS", "ATVI",
    
    # Additional Major Companies
    "V", "MA", "PYPL", "ADSK", "FISV", "FIS", "IT", "CTSH", "ACN", "TYL",
    "BR", "FLT", "GDDY", "CTXS", "NLOK", "VRSN", "JNPR", "FFIV", "AKAM", "ANET"
]

# High-volume, highly liquid subset for options strategies
OPTIONS_FOCUS_SYMBOLS = [
    "SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "TSLA", "META",
    "NFLX", "AMD", "INTC", "JPM", "BAC", "XOM", "CVX", "JNJ", "PFE", "UNH",
    "HD", "MCD", "NKE", "V", "MA", "DIS", "BA", "CAT", "GS", "MS"
]

# Popular crypto symbols (properly formatted for Alpaca)
CRYPTO_SYMBOLS = [
    "BTC/USD", "ETH/USD", "ADA/USD", "SOL/USD", "DOT/USD", "AVAX/USD",
    "MATIC/USD", "LINK/USD", "UNI/USD", "LTC/USD", "BCH/USD", "XLM/USD"
]

# Large-cap focus for fundamental analysis
FUNDAMENTAL_FOCUS_SYMBOLS = [
    "AAPL", "MSFT", "GOOGL", "AMZN", "NVDA", "TSLA", "META", "NFLX", "JPM", "BAC",
    "JNJ", "PFE", "UNH", "HD", "WMT", "PG", "KO", "MCD", "DIS", "V",
    "MA", "XOM", "CVX", "LMT", "BA", "CAT", "GE", "IBM", "ORCL", "CRM"
]

def get_sp500_symbols():
    """Return the complete S&P 500 symbol list"""
    return SP500_SYMBOLS.copy()

def get_options_symbols():
    """Return high-liquidity symbols suitable for options trading"""
    return OPTIONS_FOCUS_SYMBOLS.copy()

def get_crypto_symbols():
    """Return crypto trading pairs"""
    return CRYPTO_SYMBOLS.copy()

def get_fundamental_symbols():
    """Return large-cap symbols for fundamental analysis"""
    return FUNDAMENTAL_FOCUS_SYMBOLS.copy()

def get_symbol_batches(symbols=None, batch_size=50):
    """Split symbols into batches for parallel processing"""
    if symbols is None:
        symbols = get_sp500_symbols()
    
    batches = []
    for i in range(0, len(symbols), batch_size):
        batches.append(symbols[i:i + batch_size])
    return batches 