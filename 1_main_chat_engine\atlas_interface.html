<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. - Advanced Trading & Learning Analysis System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .main-container {
            flex: 1;
            display: flex;
            padding: 1rem;
            gap: 1rem;
            overflow: hidden;
        }

        .chat-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .panel-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: white;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .input-container {
            padding: 1rem;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 0.5rem;
        }

        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 0.75rem 1rem;
            background: #f0f0f0;
            border-radius: 10px;
            margin-bottom: 1rem;
            max-width: 80%;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            z-index: 1000;
        }

        .status-connected {
            background: #28a745;
        }

        .status-disconnected {
            background: #dc3545;
        }

        .confidence-indicator {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .message-metadata {
            font-size: 0.75rem;
            color: #888;
            margin-top: 0.5rem;
            border-top: 1px solid #eee;
            padding-top: 0.5rem;
        }

        /* Lee Method Scanner Styles */
        .scanner-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            margin-left: auto;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #28a745;
        }

        .status-dot.scanning {
            background: #ffc107;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .scanner-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .scanner-stats {
            display: flex;
            justify-content: space-between;
            padding: 1rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            font-size: 0.85rem;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
        }

        .stat-value {
            color: #333;
            font-weight: bold;
        }

        .signals-list {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .signal-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .signal-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .signal-symbol {
            font-weight: bold;
            font-size: 1.1rem;
            color: #333;
        }

        .signal-strength {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .signal-strength.very_strong {
            background: #d4edda;
            color: #155724;
        }

        .signal-strength.strong {
            background: #d1ecf1;
            color: #0c5460;
        }

        .signal-strength.moderate {
            background: #fff3cd;
            color: #856404;
        }

        .signal-strength.weak {
            background: #f8d7da;
            color: #721c24;
        }

        .signal-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #666;
        }

        .signal-price {
            font-weight: 500;
        }

        .signal-confidence {
            font-weight: bold;
            color: #28a745;
        }

        .no-signals {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #666;
            text-align: center;
        }

        .scanning-animation {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .scanning-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: scanning 1.4s infinite ease-in-out both;
        }

        .scanning-dot:nth-child(1) { animation-delay: -0.32s; }
        .scanning-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes scanning {
            0%, 80%, 100% {
                transform: scale(0);
            }
            40% {
                transform: scale(1);
            }
        }

        .scanner-controls {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .control-button {
            flex: 1;
            padding: 0.5rem 1rem;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .control-button:hover {
            background: #667eea;
            color: white;
        }

        .control-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .message {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 A.T.L.A.S.</h1>
        <p>Advanced Trading & Learning Analysis System powered by Predicto</p>
    </div>

    <div class="status-indicator status-disconnected" id="statusIndicator">
        Connecting...
    </div>

    <div class="main-container">
        <div class="chat-panel">
            <div class="panel-header">
                💬 General Trading Assistant
            </div>
            <div class="chat-messages" id="leftChatMessages">
                <div class="message assistant">
                    <strong>A.T.L.A.S. powered by Predicto</strong><br>
                    Welcome! I'm your advanced trading assistant. I can help you with stock analysis, market insights, trading strategies, and educational content. What would you like to explore today?
                </div>
            </div>
            <div class="typing-indicator" id="leftTypingIndicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                A.T.L.A.S. is thinking...
            </div>
            <div class="input-container">
                <input type="text" class="message-input" id="leftMessageInput" 
                       placeholder="Ask me about stocks, trading strategies, or market analysis..." 
                       maxlength="500">
                <button class="send-button" id="leftSendButton">Send</button>
            </div>
        </div>

        <div class="chat-panel">
            <div class="panel-header">
                🔍 Lee Method Real-Time Scanner
                <div class="scanner-status" id="scannerStatus">
                    <span class="status-dot" id="statusDot"></span>
                    <span id="statusText">Connecting...</span>
                </div>
            </div>
            <div class="scanner-container" id="scannerContainer">
                <div class="scanner-stats" id="scannerStats">
                    <div class="stat-item">
                        <span class="stat-label">Active Signals:</span>
                        <span class="stat-value" id="activeSignalsCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Last Scan:</span>
                        <span class="stat-value" id="lastScanTime">Never</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Universe:</span>
                        <span class="stat-value" id="universeSize">0</span>
                    </div>
                </div>
                <div class="signals-list" id="signalsList">
                    <div class="no-signals" id="noSignals">
                        <div class="scanning-animation">
                            <div class="scanning-dot"></div>
                            <div class="scanning-dot"></div>
                            <div class="scanning-dot"></div>
                        </div>
                        <p>Scanning for Lee Method patterns...</p>
                    </div>
                </div>
            </div>
            <div class="scanner-controls">
                <button class="control-button" id="refreshButton" onclick="refreshScanner()">
                    🔄 Refresh
                </button>
                <button class="control-button" id="toggleScannerButton" onclick="toggleScanner()">
                    ⏸️ Pause
                </button>
            </div>
        </div>
    </div>

    <script>
        class ATLASInterface {
            constructor() {
                this.apiUrl = 'http://localhost:8080/api/v1/chat';
                this.sessionId = this.generateSessionId();
                this.isConnected = false;
                this.conversationHistory = {
                    left: [],
                    right: []
                };

                // Scanner properties
                this.scannerWebSocket = null;
                this.scannerRunning = false;
                this.currentSignals = [];

                this.initializeEventListeners();
                this.checkConnection();
                this.initializeScanner();
            }

            generateSessionId() {
                return 'atlas-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            }

            initializeEventListeners() {
                // Left panel (General Trading)
                const leftInput = document.getElementById('leftMessageInput');
                const leftButton = document.getElementById('leftSendButton');
                
                leftInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage('left');
                    }
                });
                
                leftButton.addEventListener('click', () => this.sendMessage('left'));
            }

            async checkConnection() {
                try {
                    const response = await fetch('http://localhost:8080/api/v1/health');
                    if (response.ok) {
                        this.updateConnectionStatus(true);
                    } else {
                        this.updateConnectionStatus(false);
                    }
                } catch (error) {
                    this.updateConnectionStatus(false);
                }
            }

            updateConnectionStatus(connected) {
                this.isConnected = connected;
                const indicator = document.getElementById('statusIndicator');
                
                if (connected) {
                    indicator.textContent = 'Connected';
                    indicator.className = 'status-indicator status-connected';
                } else {
                    indicator.textContent = 'Disconnected';
                    indicator.className = 'status-indicator status-disconnected';
                }
            }

            async sendMessage(panel) {
                const inputId = panel + 'MessageInput';
                const input = document.getElementById(inputId);
                const message = input.value.trim();
                
                if (!message) return;
                
                // Clear input and disable button
                input.value = '';
                this.setButtonState(panel, false);
                
                // Add user message to chat
                this.addMessage(message, 'user', panel);
                
                // Show typing indicator
                this.showTypingIndicator(panel, true);
                
                try {
                    // Send to backend - NO FALLBACKS, let backend handle everything
                    const response = await this.callAPI(message, panel);
                    
                    // Hide typing indicator
                    this.showTypingIndicator(panel, false);
                    
                    // Add assistant response
                    this.addMessage(response.response, 'assistant', panel, {
                        type: response.type,
                        confidence: response.confidence,
                        analysis: response.analysis,
                        recommendations: response.recommendations
                    });
                    
                } catch (error) {
                    console.error('❌ API call failed:', error);
                    this.showTypingIndicator(panel, false);
                    
                    // Only show connection error, no content fallbacks
                    this.addMessage(
                        "⚠️ I'm having trouble connecting to the A.T.L.A.S. system right now. Please check that the server is running and try again.",
                        'assistant',
                        panel,
                        { type: 'connection_error', confidence: 0.0 }
                    );
                } finally {
                    this.setButtonState(panel, true);
                }
            }

            async callAPI(message, panel) {
                const requestData = {
                    message: message,
                    session_id: this.sessionId,
                    panel_type: panel,
                    interface_type: panel === 'right' ? 'pattern_scanner' : 'general_trading',
                    conversation_history: this.conversationHistory[panel] || []
                };

                const response = await fetch(this.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData),
                    timeout: 30000
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                // Store conversation history for context
                this.conversationHistory[panel].push({
                    user: message,
                    assistant: data.response,
                    timestamp: new Date().toISOString()
                });
                
                // Keep only last 10 exchanges for context
                if (this.conversationHistory[panel].length > 10) {
                    this.conversationHistory[panel] = this.conversationHistory[panel].slice(-10);
                }

                return {
                    response: data.response || data.message || 'I received your message but had trouble processing it.',
                    type: data.type || 'general',
                    confidence: data.confidence || 0.8,
                    analysis: data.analysis,
                    recommendations: data.recommendations
                };
            }

            addMessage(content, sender, panel, metadata = {}) {
                const messagesContainer = document.getElementById(panel + 'ChatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                let messageContent = content;
                
                // Add confidence indicator for assistant messages
                if (sender === 'assistant' && metadata.confidence !== undefined) {
                    const confidencePercent = Math.round(metadata.confidence * 100);
                    messageContent += `<div class="confidence-indicator">Confidence: ${confidencePercent}%</div>`;
                }
                
                // Add metadata if available
                if (metadata.type && metadata.type !== 'general') {
                    messageContent += `<div class="message-metadata">Type: ${metadata.type}</div>`;
                }
                
                messageDiv.innerHTML = messageContent;
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            showTypingIndicator(panel, show) {
                const indicator = document.getElementById(panel + 'TypingIndicator');
                indicator.style.display = show ? 'block' : 'none';
                
                if (show) {
                    const messagesContainer = document.getElementById(panel + 'ChatMessages');
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            }

            setButtonState(panel, enabled) {
                const button = document.getElementById(panel + 'SendButton');
                const input = document.getElementById(panel + 'MessageInput');

                button.disabled = !enabled;
                input.disabled = !enabled;

                if (enabled) {
                    input.focus();
                }
            }

            // Lee Method Scanner Methods
            async initializeScanner() {
                try {
                    // Check scanner status
                    await this.updateScannerStatus();

                    // Initialize WebSocket connection
                    this.connectScannerWebSocket();

                    // Load initial signals
                    await this.loadScannerSignals();

                } catch (error) {
                    console.error('Scanner initialization failed:', error);
                    this.updateScannerStatusDisplay('disconnected', 'Connection Failed');
                }
            }

            async updateScannerStatus() {
                try {
                    const response = await fetch('http://localhost:8080/api/v1/lee-method/scanner/status');
                    if (response.ok) {
                        const data = await response.json();
                        this.scannerRunning = data.is_running;

                        // Update UI
                        this.updateScannerStatusDisplay(
                            data.is_running ? 'scanning' : 'connected',
                            data.is_running ? 'Scanning Active' : 'Ready'
                        );

                        // Update stats
                        document.getElementById('activeSignalsCount').textContent = data.active_signals_count;
                        document.getElementById('universeSize').textContent = data.scan_universe_size;

                        if (data.stats.last_scan_time) {
                            const lastScan = new Date(data.stats.last_scan_time);
                            document.getElementById('lastScanTime').textContent = lastScan.toLocaleTimeString();
                        }
                    }
                } catch (error) {
                    console.error('Failed to update scanner status:', error);
                }
            }

            connectScannerWebSocket() {
                try {
                    const wsUrl = 'ws://localhost:8080/api/v1/lee-method/ws';
                    this.scannerWebSocket = new WebSocket(wsUrl);

                    this.scannerWebSocket.onopen = () => {
                        console.log('Scanner WebSocket connected');
                        this.updateScannerStatusDisplay('connected', 'Connected');
                    };

                    this.scannerWebSocket.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleScannerUpdate(data);
                        } catch (error) {
                            console.error('Failed to parse scanner message:', error);
                        }
                    };

                    this.scannerWebSocket.onclose = () => {
                        console.log('Scanner WebSocket disconnected');
                        this.updateScannerStatusDisplay('disconnected', 'Disconnected');

                        // Attempt to reconnect after 5 seconds
                        setTimeout(() => {
                            if (!this.scannerWebSocket || this.scannerWebSocket.readyState === WebSocket.CLOSED) {
                                this.connectScannerWebSocket();
                            }
                        }, 5000);
                    };

                    this.scannerWebSocket.onerror = (error) => {
                        console.error('Scanner WebSocket error:', error);
                        this.updateScannerStatusDisplay('disconnected', 'Connection Error');
                    };

                } catch (error) {
                    console.error('Failed to connect scanner WebSocket:', error);
                }
            }

            handleScannerUpdate(data) {
                if (data.type === 'scanner_update' || data.type === 'initial_data') {
                    this.currentSignals = data.signals || [];
                    this.updateSignalsDisplay();

                    // Update stats
                    if (data.stats) {
                        document.getElementById('activeSignalsCount').textContent = this.currentSignals.length;

                        if (data.stats.last_scan_time) {
                            const lastScan = new Date(data.stats.last_scan_time);
                            document.getElementById('lastScanTime').textContent = lastScan.toLocaleTimeString();
                        }
                    }

                    // Update scanner status
                    this.updateScannerStatusDisplay('scanning', 'Scanning Active');
                }
            }

            updateSignalsDisplay() {
                const signalsList = document.getElementById('signalsList');
                const noSignals = document.getElementById('noSignals');

                if (this.currentSignals.length === 0) {
                    noSignals.style.display = 'flex';
                    // Clear any existing signals
                    const existingSignals = signalsList.querySelectorAll('.signal-item');
                    existingSignals.forEach(signal => signal.remove());
                    return;
                }

                noSignals.style.display = 'none';

                // Clear existing signals
                const existingSignals = signalsList.querySelectorAll('.signal-item');
                existingSignals.forEach(signal => signal.remove());

                // Add new signals
                this.currentSignals.forEach(signal => {
                    const signalElement = this.createSignalElement(signal);
                    signalsList.appendChild(signalElement);
                });
            }

            createSignalElement(signal) {
                const signalDiv = document.createElement('div');
                signalDiv.className = 'signal-item';
                signalDiv.onclick = () => this.handleSignalClick(signal);

                signalDiv.innerHTML = `
                    <div class="signal-header">
                        <span class="signal-symbol">${signal.symbol}</span>
                        <span class="signal-strength ${signal.signal_strength}">${signal.signal_strength.replace('_', ' ')}</span>
                    </div>
                    <div class="signal-details">
                        <span class="signal-price">$${signal.current_price.toFixed(2)}</span>
                        <span class="signal-confidence">${signal.confidence_score.toFixed(0)}%</span>
                    </div>
                `;

                return signalDiv;
            }

            handleSignalClick(signal) {
                // Populate left chat with detailed Lee Method analysis
                const analysisMessage = `Analyze ${signal.symbol} using Lee Method - detected ${signal.signal_strength} signal with ${signal.confidence_score.toFixed(0)}% confidence`;

                // Set the message in the left input
                const leftInput = document.getElementById('leftMessageInput');
                leftInput.value = analysisMessage;

                // Automatically send the message
                this.sendMessage('left');
            }

            updateScannerStatusDisplay(status, text) {
                const statusDot = document.getElementById('statusDot');
                const statusText = document.getElementById('statusText');

                statusDot.className = `status-dot ${status}`;
                statusText.textContent = text;
            }

            async loadScannerSignals() {
                try {
                    const response = await fetch('http://localhost:8080/api/v1/lee-method/signals');
                    if (response.ok) {
                        const data = await response.json();
                        this.currentSignals = data.signals || [];
                        this.updateSignalsDisplay();
                    }
                } catch (error) {
                    console.error('Failed to load scanner signals:', error);
                }
            }
        }

        // Global scanner control functions
        async function refreshScanner() {
            if (window.atlasInterface) {
                await window.atlasInterface.loadScannerSignals();
                await window.atlasInterface.updateScannerStatus();
            }
        }

        async function toggleScanner() {
            if (!window.atlasInterface) return;

            const button = document.getElementById('toggleScannerButton');
            const isRunning = window.atlasInterface.scannerRunning;

            try {
                button.disabled = true;

                const endpoint = isRunning ? 'stop' : 'start';
                const response = await fetch(`http://localhost:8080/api/v1/lee-method/scanner/${endpoint}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    window.atlasInterface.scannerRunning = !isRunning;
                    button.textContent = isRunning ? '▶️ Start' : '⏸️ Pause';

                    // Update status
                    await window.atlasInterface.updateScannerStatus();
                }
            } catch (error) {
                console.error('Failed to toggle scanner:', error);
            } finally {
                button.disabled = false;
            }
        }

        // Initialize the interface when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.atlasInterface = new ATLASInterface();
            
            // Check connection every 30 seconds
            setInterval(() => {
                window.atlasInterface.checkConnection();
            }, 30000);
        });
    </script>
</body>
</html>
