#!/usr/bin/env python3
"""
Test Web Interface Parity
Validate that web interface produces identical results to terminal API calls
"""

import requests
import json
import time
from datetime import datetime

def test_web_vs_terminal_parity():
    """Test that web interface and terminal produce identical responses"""
    base_url = "http://localhost:8080"
    
    print("🔍 TESTING WEB INTERFACE vs TERMINAL API PARITY")
    print("=" * 60)
    
    test_cases = [
        {
            "message": "Hello",
            "description": "Simple greeting",
            "expect_type": "greeting"
        },
        {
            "message": "What can you help me with?",
            "description": "Capabilities inquiry",
            "expect_type": "capabilities"
        },
        {
            "message": "Analyze AAPL for trading",
            "description": "Trading analysis request",
            "expect_type": "guru_analysis"
        },
        {
            "message": "Should I buy Tesla stock?",
            "description": "Trading recommendation",
            "expect_type": "guru_analysis"
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: '{test['message']}'")
        print(f"   Expected: {test['description']}")
        print("-" * 50)
        
        # Terminal-style API call
        terminal_result = make_api_call(
            base_url, 
            test["message"], 
            f"terminal-{i}", 
            "terminal"
        )
        
        # Web-style API call (matching HTML interface exactly)
        web_result = make_api_call(
            base_url, 
            test["message"], 
            f"web-{i}", 
            "web"
        )
        
        # Compare results
        comparison = compare_responses(terminal_result, web_result, test)
        results.append({
            "test": test,
            "terminal": terminal_result,
            "web": web_result,
            "comparison": comparison
        })
        
        print_comparison_result(comparison, test["message"])
        time.sleep(1)
    
    # Generate summary
    generate_parity_report(results)
    
    return results

def make_api_call(base_url, message, session_id, call_type):
    """Make API call with specified parameters"""
    try:
        if call_type == "terminal":
            # Simple terminal-style call
            payload = {
                "message": message,
                "session_id": session_id
            }
        else:
            # Web-style call matching HTML interface
            payload = {
                "message": message,
                "session_id": session_id,
                "user_id": "web_user",
                "context": {
                    "interface": "web",
                    "timestamp": datetime.now().isoformat()
                }
            }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/chat",
            json=payload,
            timeout=20
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            return {
                "success": True,
                "status_code": response.status_code,
                "response_time": response_time,
                "data": data,
                "error": None
            }
        else:
            return {
                "success": False,
                "status_code": response.status_code,
                "response_time": response_time,
                "data": None,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "status_code": None,
            "response_time": None,
            "data": None,
            "error": str(e)
        }

def compare_responses(terminal_result, web_result, test_case):
    """Compare terminal and web responses for parity"""
    comparison = {
        "both_successful": False,
        "same_type": False,
        "similar_length": False,
        "similar_confidence": False,
        "contains_expected_content": False,
        "response_time_acceptable": False,
        "overall_parity": False
    }
    
    if not terminal_result["success"] or not web_result["success"]:
        comparison["error"] = "One or both requests failed"
        return comparison
    
    comparison["both_successful"] = True
    
    terminal_data = terminal_result["data"]
    web_data = web_result["data"]
    
    # Compare response types
    terminal_type = terminal_data.get("type", "unknown")
    web_type = web_data.get("type", "unknown")
    comparison["same_type"] = terminal_type == web_type
    
    # Compare response lengths (should be similar)
    terminal_length = len(terminal_data.get("response", ""))
    web_length = len(web_data.get("response", ""))
    comparison["similar_length"] = abs(terminal_length - web_length) < 100
    
    # Compare confidence scores
    terminal_conf = terminal_data.get("confidence", 0)
    web_conf = web_data.get("confidence", 0)
    comparison["similar_confidence"] = abs(terminal_conf - web_conf) < 0.2
    
    # Check for expected content based on test case
    web_response = web_data.get("response", "").lower()
    if test_case["expect_type"] == "greeting":
        comparison["contains_expected_content"] = any(word in web_response for word in ["hello", "hi", "welcome"])
    elif test_case["expect_type"] == "capabilities":
        comparison["contains_expected_content"] = any(word in web_response for word in ["analyze", "trading", "help"])
    elif test_case["expect_type"] == "guru_analysis":
        comparison["contains_expected_content"] = any(phrase in web_response for phrase in ["why this trade", "confidence", "$"])
    
    # Check response times
    comparison["response_time_acceptable"] = (
        terminal_result["response_time"] < 30 and 
        web_result["response_time"] < 30
    )
    
    # Overall parity assessment
    comparison["overall_parity"] = (
        comparison["both_successful"] and
        comparison["same_type"] and
        comparison["similar_length"] and
        comparison["contains_expected_content"] and
        comparison["response_time_acceptable"]
    )
    
    # Store detailed data for analysis
    comparison["details"] = {
        "terminal_type": terminal_type,
        "web_type": web_type,
        "terminal_length": terminal_length,
        "web_length": web_length,
        "terminal_confidence": terminal_conf,
        "web_confidence": web_conf,
        "terminal_time": terminal_result["response_time"],
        "web_time": web_result["response_time"]
    }
    
    return comparison

def print_comparison_result(comparison, message):
    """Print comparison results"""
    if comparison["overall_parity"]:
        print("   🎉 PERFECT PARITY - Web interface matches terminal exactly!")
    else:
        print("   ⚠️ PARITY ISSUES DETECTED:")
        
        if not comparison["both_successful"]:
            print("      ❌ Request failures")
        if not comparison["same_type"]:
            print(f"      ❌ Type mismatch: {comparison['details']['terminal_type']} vs {comparison['details']['web_type']}")
        if not comparison["similar_length"]:
            print(f"      ❌ Length difference: {comparison['details']['terminal_length']} vs {comparison['details']['web_length']}")
        if not comparison["similar_confidence"]:
            print(f"      ❌ Confidence difference: {comparison['details']['terminal_confidence']:.2f} vs {comparison['details']['web_confidence']:.2f}")
        if not comparison["contains_expected_content"]:
            print("      ❌ Missing expected content")
        if not comparison["response_time_acceptable"]:
            print(f"      ❌ Slow response times: {comparison['details']['terminal_time']:.1f}s vs {comparison['details']['web_time']:.1f}s")

def generate_parity_report(results):
    """Generate comprehensive parity report"""
    print("\n" + "=" * 60)
    print("📋 WEB INTERFACE PARITY REPORT")
    print("=" * 60)
    
    total_tests = len(results)
    perfect_parity = sum(1 for r in results if r["comparison"]["overall_parity"])
    
    print(f"📊 Overall Results:")
    print(f"   • Total Tests: {total_tests}")
    print(f"   • Perfect Parity: {perfect_parity}")
    print(f"   • Issues Found: {total_tests - perfect_parity}")
    print(f"   • Parity Rate: {perfect_parity/total_tests*100:.1f}%")
    
    print(f"\n📈 Detailed Analysis:")
    
    # Analyze specific issues
    type_mismatches = sum(1 for r in results if not r["comparison"]["same_type"])
    length_issues = sum(1 for r in results if not r["comparison"]["similar_length"])
    content_issues = sum(1 for r in results if not r["comparison"]["contains_expected_content"])
    
    print(f"   • Type Mismatches: {type_mismatches}/{total_tests}")
    print(f"   • Length Issues: {length_issues}/{total_tests}")
    print(f"   • Content Issues: {content_issues}/{total_tests}")
    
    if perfect_parity == total_tests:
        print("\n🎉 PERFECT WEB INTERFACE PARITY ACHIEVED!")
        print("   ✅ Web interface produces identical results to terminal API")
        print("   ✅ All response types match correctly")
        print("   ✅ All content formatting is consistent")
        print("   ✅ Response times are acceptable")
        print("   ✅ Users will get the same experience regardless of access method")
    elif perfect_parity >= total_tests * 0.8:
        print("\n✅ GOOD WEB INTERFACE PARITY")
        print("   ✅ Most functionality working correctly")
        print("   ⚠️ Minor issues present but system is usable")
    else:
        print("\n⚠️ WEB INTERFACE PARITY ISSUES")
        print("   ❌ Significant discrepancies between web and terminal")
        print("   ❌ Users may get different experiences")
        
    # Save detailed results
    with open('web_interface_parity_results.json', 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'parity_rate': perfect_parity/total_tests*100,
            'perfect_parity': perfect_parity,
            'total_tests': total_tests,
            'detailed_results': results
        }, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: web_interface_parity_results.json")

def test_browser_console_debugging():
    """Instructions for browser console testing"""
    print("\n🌐 BROWSER CONSOLE TESTING INSTRUCTIONS")
    print("=" * 60)
    print("1. Open the web interface in your browser")
    print("2. Open Developer Tools (F12)")
    print("3. Go to the Console tab")
    print("4. Send a test message and watch for:")
    print("   • 'Sending message to API: [message]'")
    print("   • 'Request data: [object]'")
    print("   • 'Response status: 200'")
    print("   • 'API response data: [object]'")
    print("   • 'Adding message: [object]'")
    print("   • 'Message added successfully'")
    print("\n5. If you see errors, check:")
    print("   • Network tab for failed requests")
    print("   • Console for JavaScript errors")
    print("   • Response data structure")

if __name__ == "__main__":
    print("🚀 Starting Web Interface Parity Testing...")
    results = test_web_vs_terminal_parity()
    test_browser_console_debugging()
    
    print("\n🎯 Testing complete! Check the browser console for real-time debugging.")
