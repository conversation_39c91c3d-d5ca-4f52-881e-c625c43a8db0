#!/usr/bin/env python3
"""
Simple API test to see actual responses
"""
import requests
import json

def test_api():
    url = "http://localhost:8080/api/v1/chat"
    payload = {
        "message": "hello",
        "session_id": "test123"
    }
    
    try:
        print("Testing API...")
        response = requests.post(url, json=payload, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data.get('response', 'No response')}")
            print(f"Type: {data.get('type', 'No type')}")
            print(f"Confidence: {data.get('confidence', 'No confidence')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api()
