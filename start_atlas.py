#!/usr/bin/env python3
"""
ATLAS v2 Launcher
Full access to ALL Financial Modeling Prep and Alpaca endpoints
"""

import os
import sys
import time
import subprocess
import webbrowser
import requests
from pathlib import Path

def check_server(url, timeout=30):
    """Check if server is running"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return True
        except:
            pass
        print(f"   Waiting for server... ({int(time.time() - start_time)}s)")
        time.sleep(5)
    return False

def main():
    print("=" * 50)
    print("   🤖 ATLAS Trading Assistant v2")
    print("   Full Access to ALL FMP & Alpaca Endpoints")
    print("=" * 50)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check if .env exists
    if not Path('.env').exists() and Path('../.env').exists():
        # Copy parent .env if it exists
        import shutil
        shutil.copy('../.env', '.env')
        print("✅ Environment file configured")
    
    # Start the server
    print("🚀 Starting ATLAS server...")
    
    # Use the Python from the virtual environment if available
    python_cmd = sys.executable
    
    # Start server in subprocess
    server_process = subprocess.Popen(
        [python_cmd, "atlas_web.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1
    )
    
    # Wait for server to start
    server_url = "http://localhost:8000"
    if check_server(server_url):
        print("✅ Server started successfully!")
        print(f"🌐 Opening ATLAS in your browser...")
        webbrowser.open(server_url)
        print("\n📊 ATLAS is ready to trade!")
        print("Press Ctrl+C to stop the server\n")
        
        # Keep the process running and show output
        try:
            while True:
                output = server_process.stdout.readline()
                if output:
                    print(output.strip())
                if server_process.poll() is not None:
                    break
        except KeyboardInterrupt:
            print("\n🛑 Shutting down ATLAS...")
            server_process.terminate()
    else:
        print("❌ Server failed to start")
        server_process.terminate()
        
        # Show error output
        stderr = server_process.stderr.read()
        if stderr:
            print("\nError details:")
            print(stderr)

if __name__ == "__main__":
    main() 