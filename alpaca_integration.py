"""
Alpaca Account Integration for ATLAS
Provides real-time account information, positions, and portfolio data
"""
import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
from dotenv import load_dotenv
import asyncio
from api_bridge import call_alpaca_api

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger("ATLAS_ALPACA")

class AlpacaAccount:
    """
    Manages Alpaca account integration for ATLAS
    """
    def __init__(self):
        self._account_cache = None
        self._cache_timestamp = None
        self._cache_duration = 60  # Cache for 60 seconds

    def _is_cache_valid(self) -> bool:
        if not self._cache_timestamp:
            return False
        return (datetime.now() - self._cache_timestamp).seconds < self._cache_duration

    async def aget_account_info(self) -> Dict[str, Any]:
        try:
            if self._is_cache_valid() and self._account_cache:
                return self._account_cache
            result = await call_alpaca_api("account")
            if result["success"]:
                account_data = result["data"]
                formatted_data = {
                    "account_id": account_data.get("id"),
                    "status": account_data.get("status"),
                    "currency": account_data.get("currency", "USD"),
                    "buying_power": float(account_data.get("buying_power", 0)),
                    "cash": float(account_data.get("cash", 0)),
                    "portfolio_value": float(account_data.get("portfolio_value", 0)),
                    "long_market_value": float(account_data.get("long_market_value", 0)),
                    "short_market_value": float(account_data.get("short_market_value", 0)),
                    "equity": float(account_data.get("equity", 0)),
                    "last_equity": float(account_data.get("last_equity", 0)),
                    "initial_margin": float(account_data.get("initial_margin", 0)),
                    "maintenance_margin": float(account_data.get("maintenance_margin", 0)),
                    "pattern_day_trader": account_data.get("pattern_day_trader", False),
                    "trade_suspended_by_user": account_data.get("trade_suspended_by_user", False),
                    "trading_blocked": account_data.get("trading_blocked", False),
                    "transfers_blocked": account_data.get("transfers_blocked", False),
                    "account_blocked": account_data.get("account_blocked", False),
                    "created_at": account_data.get("created_at"),
                    "daytrade_count": account_data.get("daytrade_count", 0),
                    "daytrading_buying_power": float(account_data.get("daytrading_buying_power", 0)),
                    "regt_buying_power": float(account_data.get("regt_buying_power", 0)),
                    "sma": float(account_data.get("sma", 0))
                }
                formatted_data["total_profit_loss"] = formatted_data["equity"] - formatted_data["last_equity"]
                formatted_data["profit_loss_percent"] = (
                    (formatted_data["total_profit_loss"] / formatted_data["last_equity"] * 100)
                    if formatted_data["last_equity"] > 0 else 0
                )
                self._account_cache = formatted_data
                self._cache_timestamp = datetime.now()
                return formatted_data
            else:
                logger.error(f"Failed to get account info: {result['status_code']} - {result['data']}")
                return {"error": f"Failed to retrieve account information: {result['status_code']}"}
        except Exception as e:
            logger.error(f"Error getting account info: {str(e)}")
            return {"error": f"Error accessing account: {str(e)}"}

    def get_account_info(self) -> Dict[str, Any]:
        return asyncio.run(self.aget_account_info())

    async def aget_positions(self) -> List[Dict[str, Any]]:
        try:
            result = await call_alpaca_api("positions")
            if result["success"]:
                positions = result["data"]
                formatted_positions = []
                for pos in positions:
                    formatted_pos = {
                        "symbol": pos.get("symbol"),
                        "quantity": float(pos.get("qty", 0)),
                        "side": pos.get("side"),
                        "market_value": float(pos.get("market_value", 0)),
                        "cost_basis": float(pos.get("cost_basis", 0)),
                        "unrealized_pl": float(pos.get("unrealized_pl", 0)),
                        "unrealized_plpc": float(pos.get("unrealized_plpc", 0)),
                        "unrealized_intraday_pl": float(pos.get("unrealized_intraday_pl", 0)),
                        "unrealized_intraday_plpc": float(pos.get("unrealized_intraday_plpc", 0)),
                        "current_price": float(pos.get("current_price", 0)),
                        "lastday_price": float(pos.get("lastday_price", 0)),
                        "change_today": float(pos.get("change_today", 0)),
                        "avg_entry_price": float(pos.get("avg_entry_price", 0)),
                        "asset_class": pos.get("asset_class"),
                        "asset_id": pos.get("asset_id"),
                        "exchange": pos.get("exchange")
                    }
                    if formatted_pos["quantity"] > 0:
                        formatted_pos["total_return_percent"] = (
                            (formatted_pos["unrealized_pl"] / formatted_pos["cost_basis"] * 100)
                            if formatted_pos["cost_basis"] > 0 else 0
                        )
                    formatted_positions.append(formatted_pos)
                return formatted_positions
            else:
                logger.error(f"Failed to get positions: {result['status_code']}")
                return []
        except Exception as e:
            logger.error(f"Error getting positions: {str(e)}")
            return []

    def get_positions(self) -> List[Dict[str, Any]]:
        return asyncio.run(self.aget_positions())

    async def aget_orders(self, status: str = "all", limit: int = 100) -> List[Dict[str, Any]]:
        try:
            params = {
                "status": status,
                "limit": limit,
                "direction": "desc"
            }
            result = await call_alpaca_api("orders", query_params=params)
            if result["success"]:
                orders = result["data"]
                formatted_orders = []
                for order in orders:
                    formatted_order = {
                        "id": order.get("id"),
                        "client_order_id": order.get("client_order_id"),
                        "created_at": order.get("created_at"),
                        "updated_at": order.get("updated_at"),
                        "submitted_at": order.get("submitted_at"),
                        "filled_at": order.get("filled_at"),
                        "expired_at": order.get("expired_at"),
                        "canceled_at": order.get("canceled_at"),
                        "failed_at": order.get("failed_at"),
                        "symbol": order.get("symbol"),
                        "asset_class": order.get("asset_class"),
                        "quantity": float(order.get("qty", 0)),
                        "filled_qty": float(order.get("filled_qty", 0)),
                        "type": order.get("type"),
                        "side": order.get("side"),
                        "time_in_force": order.get("time_in_force"),
                        "limit_price": float(order.get("limit_price", 0)) if order.get("limit_price") else None,
                        "stop_price": float(order.get("stop_price", 0)) if order.get("stop_price") else None,
                        "filled_avg_price": float(order.get("filled_avg_price", 0)) if order.get("filled_avg_price") else None,
                        "status": order.get("status"),
                        "extended_hours": order.get("extended_hours", False),
                        "order_class": order.get("order_class"),
                        "legs": order.get("legs", [])
                    }
                    formatted_orders.append(formatted_order)
                return formatted_orders
            else:
                logger.error(f"Failed to get orders: {result['status_code']}")
                return []
        except Exception as e:
            logger.error(f"Error getting orders: {str(e)}")
            return []

    def get_orders(self, status: str = "all", limit: int = 100) -> List[Dict[str, Any]]:
        return asyncio.run(self.aget_orders(status, limit))

    async def aget_portfolio_history(self, period: str = "1D", timeframe: str = "1Min") -> Dict[str, Any]:
        try:
            params = {
                "period": period,
                "timeframe": timeframe,
                "extended_hours": True
            }
            result = await call_alpaca_api("account/portfolio/history", query_params=params)
            if result["success"]:
                history = result["data"]
                if history.get("timestamp") and history.get("equity"):
                    df = pd.DataFrame({
                        "timestamp": pd.to_datetime(history["timestamp"], unit='s'),
                        "equity": history["equity"],
                        "profit_loss": history.get("profit_loss", [0] * len(history["equity"])),
                        "profit_loss_pct": history.get("profit_loss_pct", [0] * len(history["equity"]))
                    })
                    df["returns"] = df["equity"].pct_change()
                    df["cumulative_returns"] = (1 + df["returns"]).cumprod() - 1
                    return {
                        "base_value": history.get("base_value", 0),
                        "timeframe": timeframe,
                        "history": df.to_dict('records'),
                        "current_equity": df["equity"].iloc[-1] if not df.empty else 0,
                        "total_return": df["cumulative_returns"].iloc[-1] if not df.empty else 0,
                        "max_equity": df["equity"].max() if not df.empty else 0,
                        "min_equity": df["equity"].min() if not df.empty else 0
                    }
                else:
                    return {"error": "No portfolio history available"}
            else:
                logger.error(f"Failed to get portfolio history: {result['status_code']}")
                return {"error": f"Failed to retrieve portfolio history: {result['status_code']}"}
        except Exception as e:
            logger.error(f"Error getting portfolio history: {str(e)}")
            return {"error": f"Error accessing portfolio history: {str(e)}"}

    def get_portfolio_history(self, period: str = "1D", timeframe: str = "1Min") -> Dict[str, Any]:
        return asyncio.run(self.aget_portfolio_history(period, timeframe))

    async def aget_account_activities(self, activity_types: List[str] = None) -> List[Dict[str, Any]]:
        try:
            params = {}
            if activity_types:
                params["activity_types"] = ",".join(activity_types)
            result = await call_alpaca_api("account/activities", query_params=params)
            if result["success"]:
                return result["data"]
            else:
                logger.error(f"Failed to get account activities: {result['status_code']}")
                return []
        except Exception as e:
            logger.error(f"Error getting account activities: {str(e)}")
            return []

    def get_account_activities(self, activity_types: List[str] = None) -> List[Dict[str, Any]]:
        return asyncio.run(self.aget_account_activities(activity_types))

    async def aget_clock(self) -> Dict[str, Any]:
        try:
            result = await call_alpaca_api("clock")
            if result["success"]:
                return result["data"]
            else:
                return {"is_open": False, "error": "Failed to get market status"}
        except Exception as e:
            logger.error(f"Error getting market clock: {str(e)}")
            return {"is_open": False, "error": str(e)}

    def get_clock(self) -> Dict[str, Any]:
        return asyncio.run(self.aget_clock())

    async def aget_account_summary(self) -> Dict[str, Any]:
        try:
            account_info = await self.aget_account_info()
            positions = await self.aget_positions()
            open_orders = await self.aget_orders(status="open")
            portfolio_history = await self.aget_portfolio_history(period="1D")
            market_status = await self.aget_clock()
            
            total_positions_value = sum(pos["market_value"] for pos in positions)
            total_unrealized_pl = sum(pos["unrealized_pl"] for pos in positions)
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "market_status": {
                    "is_open": market_status.get("is_open", False),
                    "next_open": market_status.get("next_open"),
                    "next_close": market_status.get("next_close")
                },
                "account": {
                    "status": account_info.get("status"),
                    "buying_power": account_info.get("buying_power", 0),
                    "cash": account_info.get("cash", 0),
                    "portfolio_value": account_info.get("portfolio_value", 0),
                    "day_trade_count": account_info.get("daytrade_count", 0),
                    "pattern_day_trader": account_info.get("pattern_day_trader", False)
                },
                "positions": {
                    "count": len(positions),
                    "total_value": total_positions_value,
                    "total_unrealized_pl": total_unrealized_pl,
                    "details": positions
                },
                "orders": {
                    "open_count": len(open_orders),
                    "open_orders": open_orders
                },
                "performance": {
                    "today_pl": account_info.get("total_profit_loss", 0),
                    "today_pl_percent": account_info.get("profit_loss_percent", 0),
                    "portfolio_history": portfolio_history
                }
            }
            
            return summary
        except Exception as e:
            logger.error(f"Error creating account summary: {str(e)}")
            return {"error": f"Failed to create account summary: {str(e)}"}

    def get_account_summary(self) -> Dict[str, Any]:
        return asyncio.run(self.aget_account_summary())

    async def aget_historical_bars(self, symbol: str, timeframe: str = "1Min", limit: int = 1000) -> pd.DataFrame:
        """
        Fetch historical OHLCV bars for a symbol from Alpaca.
        :param symbol: Stock symbol (e.g., 'AAPL')
        :param timeframe: Bar timeframe (e.g., '1Min', '5Min')
        :param limit: Number of bars to fetch (max 1000 per request)
        :return: DataFrame with columns: time, open, high, low, close, volume
        """
        endpoint = f"stocks/{symbol}/bars"
        params = {"timeframe": timeframe, "limit": limit}
        result = await call_alpaca_api(endpoint, query_params=params)
        if not result["success"] or "data" not in result or "bars" not in result["data"]:
            raise Exception(f"Failed to fetch bars for {symbol}: {result}")
        bars = result["data"]["bars"]
        if not bars:
            return pd.DataFrame()
        df = pd.DataFrame(bars)
        # Convert timestamp to pandas datetime
        if "t" in df.columns:
            df["time"] = pd.to_datetime(df["t"])
        # Rename columns to standard names
        df = df.rename(columns={"o": "open", "h": "high", "l": "low", "c": "close", "v": "volume"})
        return df[["time", "open", "high", "low", "close", "volume"]]


# Singleton instance
_alpaca_account = None

def get_alpaca_account() -> AlpacaAccount:
    """Get or create Alpaca account instance"""
    global _alpaca_account
    if _alpaca_account is None:
        _alpaca_account = AlpacaAccount()
    return _alpaca_account


class AlpacaWebSocketClient:
    """
    WebSocket client for real-time Alpaca data streaming
    """
    
    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.ws_url = "wss://stream.data.alpaca.markets/v2/sip"
        self.trade_ws_url = "wss://paper-api.alpaca.markets/stream"
        self.ws = None
        self.trade_ws = None
        self.callbacks = {
            "trades": [],
            "quotes": [],
            "bars": [],
            "trade_updates": []
        }
        
    def subscribe_trades(self, symbols: List[str], callback):
        """Subscribe to real-time trades for symbols"""
        self.callbacks["trades"].append(callback)
        # Implementation would connect to WebSocket and stream trades
        
    def subscribe_quotes(self, symbols: List[str], callback):
        """Subscribe to real-time quotes for symbols"""
        self.callbacks["quotes"].append(callback)
        # Implementation would connect to WebSocket and stream quotes
        
    def subscribe_trade_updates(self, callback):
        """Subscribe to trade execution updates"""
        self.callbacks["trade_updates"].append(callback)
        # Implementation would connect to trade WebSocket for order updates
        
    def connect(self):
        """Connect to WebSocket streams"""
        # This would establish WebSocket connections
        pass
        
    def disconnect(self):
        """Disconnect from WebSocket streams"""
        # This would close WebSocket connections
        pass 