"""
A.T.L.A.S. Comprehensive Test Suite
Automated testing for all 25+ system capabilities with detailed validation
"""

import asyncio
import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestStatus(Enum):
    PASS = "PASS"
    FAIL = "FAIL"
    SKIP = "SKIP"
    ERROR = "ERROR"

@dataclass
class TestResult:
    test_name: str
    category: str
    status: TestStatus
    score: float  # 0-10
    details: str
    response_time: float
    expected_criteria: List[str]
    actual_results: Dict[str, Any]

class ATLASTestSuite:
    """Comprehensive test suite for A.T.L.A.S. trading system"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session_id = f"test-{int(time.time())}"
        self.test_results: List[TestResult] = []
        self.total_tests = 0
        self.passed_tests = 0
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run comprehensive test suite covering all A.T.L.A.S. capabilities"""
        logger.info("🚀 Starting A.T.L.A.S. Comprehensive Test Suite")
        logger.info("=" * 60)
        
        start_time = time.time()
        
        # Test Categories
        test_categories = [
            ("System Health", self.test_system_health),
            ("Conversational AI", self.test_conversational_ai),
            ("Lee Method Scanner", self.test_lee_method_scanner),
            ("Stock Analysis", self.test_stock_analysis),
            ("Trading God Engine", self.test_trading_god_engine),
            ("Market Data Integration", self.test_market_data),
            ("Options Trading", self.test_options_trading),
            ("Portfolio Management", self.test_portfolio_management),
            ("Risk Management", self.test_risk_management),
            ("Pattern Detection", self.test_pattern_detection),
            ("Backtesting", self.test_backtesting),
            ("Real-time Features", self.test_realtime_features)
        ]
        
        # Run all test categories
        for category_name, test_method in test_categories:
            logger.info(f"\n📋 Testing Category: {category_name}")
            logger.info("-" * 40)
            
            try:
                await test_method()
            except Exception as e:
                logger.error(f"❌ Category {category_name} failed: {e}")
                self.add_test_result(
                    f"{category_name} - Critical Error",
                    category_name,
                    TestStatus.ERROR,
                    0.0,
                    f"Category test failed: {e}",
                    0.0,
                    ["Category should execute without errors"],
                    {"error": str(e)}
                )
        
        # Generate final report
        total_time = time.time() - start_time
        return self.generate_final_report(total_time)
    
    async def test_system_health(self):
        """Test basic system health and connectivity"""
        
        # Test 1: Health endpoint
        result = await self.make_request("GET", "/api/v1/health")
        self.evaluate_test(
            "System Health Check",
            "System Health",
            result,
            ["status_code == 200", "response contains status"],
            lambda r: r.get("status_code") == 200 and "status" in r.get("data", {})
        )
        
        # Test 2: Server startup time
        result = await self.make_request("GET", "/api/v1/initialization/status")
        self.evaluate_test(
            "Initialization Status",
            "System Health",
            result,
            ["status_code == 200", "components initialized"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_conversational_ai(self):
        """Test conversational AI capabilities"""
        
        test_cases = [
            {
                "name": "Basic Greeting",
                "message": "Hello, what can you help me with?",
                "criteria": ["response_length > 50", "mentions A.T.L.A.S.", "helpful tone"]
            },
            {
                "name": "Lee Method Question",
                "message": "What are the 3 criteria for the Lee Method?",
                "criteria": ["mentions 3 criteria", "histogram bars", "momentum", "timeframe"]
            },
            {
                "name": "Trading Education",
                "message": "Explain what TTM Squeeze means",
                "criteria": ["explains TTM Squeeze", "educational content", "trading context"]
            },
            {
                "name": "Context Awareness",
                "message": "Can you elaborate on that?",
                "criteria": ["maintains context", "relevant follow-up", "coherent response"]
            }
        ]
        
        for test_case in test_cases:
            result = await self.make_chat_request(test_case["message"])
            self.evaluate_chat_test(
                test_case["name"],
                "Conversational AI",
                result,
                test_case["criteria"]
            )
    
    async def test_lee_method_scanner(self):
        """Test Lee Method real-time scanner"""
        
        # Test 1: Scanner status
        result = await self.make_request("GET", "/api/v1/lee-method/scanner/status")
        self.evaluate_test(
            "Scanner Status",
            "Lee Method Scanner",
            result,
            ["status_code == 200", "is_running field", "stats available"],
            lambda r: r.get("status_code") == 200 and "is_running" in r.get("data", {})
        )
        
        # Test 2: Current signals
        result = await self.make_request("GET", "/api/v1/lee-method/signals")
        self.evaluate_test(
            "Scanner Signals",
            "Lee Method Scanner",
            result,
            ["status_code == 200", "signals array", "count field"],
            lambda r: r.get("status_code") == 200 and "signals" in r.get("data", {})
        )
        
        # Test 3: Scanner control
        result = await self.make_request("POST", "/api/v1/lee-method/scanner/start")
        self.evaluate_test(
            "Scanner Control",
            "Lee Method Scanner",
            result,
            ["status_code == 200", "success message"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_stock_analysis(self):
        """Test stock analysis capabilities"""
        
        test_symbols = ["AAPL", "TSLA", "MSFT", "GOOGL"]
        
        for symbol in test_symbols:
            # Test stock analysis via chat
            result = await self.make_chat_request(f"analyze {symbol}")
            self.evaluate_chat_test(
                f"Stock Analysis - {symbol}",
                "Stock Analysis",
                result,
                ["mentions symbol", "price information", "trading recommendation", "confidence score"]
            )
    
    async def test_trading_god_engine(self):
        """Test Trading God 6-point format"""
        
        result = await self.make_chat_request("recommend a good stock to trade today")
        
        # Check for 6-point format
        response_text = result.get("data", {}).get("response", "")
        six_point_criteria = [
            "Why This Trade" in response_text or "Why Trade" in response_text,
            "Win/Loss Probabilities" in response_text or "Probabilities" in response_text,
            "Money In" in response_text or "Investment" in response_text,
            "Stop Plans" in response_text or "Stop Loss" in response_text,
            "Market Context" in response_text or "Context" in response_text,
            "Confidence Score" in response_text or "Confidence" in response_text
        ]
        
        score = sum(six_point_criteria) / len(six_point_criteria) * 10
        
        self.add_test_result(
            "Trading God 6-Point Format",
            "Trading God Engine",
            TestStatus.PASS if score >= 8.0 else TestStatus.FAIL,
            score,
            f"6-point format compliance: {sum(six_point_criteria)}/6 criteria met",
            result.get("response_time", 0),
            ["All 6 points present", "Confident tone", "Specific recommendations"],
            {"six_point_score": sum(six_point_criteria), "response": response_text[:200]}
        )
    
    async def test_market_data(self):
        """Test market data integration"""
        
        # Test quote endpoint
        result = await self.make_request("GET", "/api/v1/quote/AAPL")
        self.evaluate_test(
            "Real-time Quote",
            "Market Data Integration",
            result,
            ["status_code == 200", "price data", "volume data"],
            lambda r: r.get("status_code") == 200
        )
        
        # Test market scan
        result = await self.make_request("GET", "/api/v1/scan")
        self.evaluate_test(
            "Market Scan",
            "Market Data Integration",
            result,
            ["status_code == 200", "signals array"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_options_trading(self):
        """Test options trading capabilities"""
        
        options_data = {
            "symbol": "AAPL",
            "option_type": "call",
            "strike_price": 180.0,
            "expiration_days": 30
        }
        
        result = await self.make_request("POST", "/api/v1/options/analyze", data=options_data)
        self.evaluate_test(
            "Options Analysis",
            "Options Trading",
            result,
            ["status_code == 200", "option price", "greeks"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_portfolio_management(self):
        """Test portfolio management features"""
        
        # Test portfolio overview
        result = await self.make_request("GET", "/api/v1/portfolio")
        self.evaluate_test(
            "Portfolio Overview",
            "Portfolio Management",
            result,
            ["status_code == 200", "positions data"],
            lambda r: r.get("status_code") == 200
        )
        
        # Test portfolio optimization
        result = await self.make_request("GET", "/api/v1/portfolio/optimization")
        self.evaluate_test(
            "Portfolio Optimization",
            "Portfolio Management",
            result,
            ["status_code == 200", "optimization data"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_risk_management(self):
        """Test risk management capabilities"""
        
        risk_data = {
            "symbol": "TSLA",
            "position_size": 100,
            "timeframe": "1d"
        }
        
        result = await self.make_request("POST", "/api/v1/risk-assessment", data=risk_data)
        self.evaluate_test(
            "Risk Assessment",
            "Risk Management",
            result,
            ["status_code == 200", "risk metrics"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_pattern_detection(self):
        """Test pattern detection beyond Lee Method"""
        
        result = await self.make_chat_request("scan for TTM squeeze patterns")
        self.evaluate_chat_test(
            "TTM Squeeze Detection",
            "Pattern Detection",
            result,
            ["TTM squeeze", "pattern detection", "specific stocks"]
        )
    
    async def test_backtesting(self):
        """Test backtesting capabilities"""
        
        backtest_data = {
            "strategy": "ttm_squeeze",
            "symbol": "AAPL",
            "start_date": "2024-01-01",
            "end_date": "2024-06-01",
            "initial_capital": 10000
        }
        
        result = await self.make_request("POST", "/api/v1/backtest", data=backtest_data)
        self.evaluate_test(
            "Strategy Backtesting",
            "Backtesting",
            result,
            ["status_code == 200", "backtest results"],
            lambda r: r.get("status_code") == 200
        )
    
    async def test_realtime_features(self):
        """Test real-time features and WebSocket connections"""
        
        # Test WebSocket endpoint availability (connection test)
        result = await self.make_chat_request("What's the current market status?")
        self.evaluate_chat_test(
            "Real-time Market Status",
            "Real-time Features",
            result,
            ["market information", "current data", "real-time"]
        )
    
    async def make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to A.T.L.A.S. API"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method == "GET":
                response = requests.get(url, timeout=10)
            elif method == "POST":
                response = requests.post(url, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = time.time() - start_time
            
            try:
                response_data = response.json()
            except:
                response_data = {"text": response.text}
            
            return {
                "status_code": response.status_code,
                "data": response_data,
                "response_time": response_time,
                "success": response.status_code == 200
            }
            
        except Exception as e:
            return {
                "status_code": 0,
                "data": {"error": str(e)},
                "response_time": time.time() - start_time,
                "success": False
            }
    
    async def make_chat_request(self, message: str) -> Dict[str, Any]:
        """Make chat request to A.T.L.A.S."""
        return await self.make_request("POST", "/api/v1/chat", {
            "message": message,
            "session_id": self.session_id
        })
    
    def evaluate_test(self, test_name: str, category: str, result: Dict[str, Any], 
                     criteria: List[str], evaluation_func) -> None:
        """Evaluate test result against criteria"""
        
        try:
            success = evaluation_func(result)
            status = TestStatus.PASS if success else TestStatus.FAIL
            score = 10.0 if success else 0.0
            
            if result.get("success", False):
                score = min(10.0, score + (5.0 if result.get("response_time", 0) < 2.0 else 0.0))
            
        except Exception as e:
            status = TestStatus.ERROR
            score = 0.0
            success = False
        
        self.add_test_result(
            test_name,
            category,
            status,
            score,
            f"Success: {success}, Response time: {result.get('response_time', 0):.2f}s",
            result.get("response_time", 0),
            criteria,
            result
        )
    
    def evaluate_chat_test(self, test_name: str, category: str, result: Dict[str, Any], 
                          criteria: List[str]) -> None:
        """Evaluate chat test result"""
        
        response_text = result.get("data", {}).get("response", "").lower()
        response_type = result.get("data", {}).get("type", "")
        confidence = result.get("data", {}).get("confidence", 0)
        
        # Score based on multiple factors
        score = 0.0
        
        # Basic response check
        if result.get("status_code") == 200 and len(response_text) > 20:
            score += 3.0
        
        # Content quality check
        if any(criterion.lower() in response_text for criterion in criteria):
            score += 4.0
        
        # Response type and confidence
        if response_type and confidence > 0.5:
            score += 2.0
        
        # Response time bonus
        if result.get("response_time", 0) < 3.0:
            score += 1.0
        
        status = TestStatus.PASS if score >= 7.0 else TestStatus.FAIL
        
        self.add_test_result(
            test_name,
            category,
            status,
            score,
            f"Response quality: {score}/10, Length: {len(response_text)}, Type: {response_type}",
            result.get("response_time", 0),
            criteria,
            {
                "response_preview": response_text[:100],
                "type": response_type,
                "confidence": confidence
            }
        )
    
    def add_test_result(self, test_name: str, category: str, status: TestStatus, 
                       score: float, details: str, response_time: float,
                       criteria: List[str], results: Dict[str, Any]) -> None:
        """Add test result to collection"""
        
        self.test_results.append(TestResult(
            test_name=test_name,
            category=category,
            status=status,
            score=score,
            details=details,
            response_time=response_time,
            expected_criteria=criteria,
            actual_results=results
        ))
        
        self.total_tests += 1
        if status == TestStatus.PASS:
            self.passed_tests += 1
        
        # Log result
        status_emoji = "✅" if status == TestStatus.PASS else "❌" if status == TestStatus.FAIL else "⚠️"
        logger.info(f"{status_emoji} {test_name}: {status.value} ({score:.1f}/10)")
    
    def generate_final_report(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        
        # Calculate statistics
        pass_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        avg_score = sum(r.score for r in self.test_results) / len(self.test_results) if self.test_results else 0
        avg_response_time = sum(r.response_time for r in self.test_results) / len(self.test_results) if self.test_results else 0
        
        # Group by category
        categories = {}
        for result in self.test_results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)
        
        # Generate report
        report = {
            "summary": {
                "total_tests": self.total_tests,
                "passed_tests": self.passed_tests,
                "failed_tests": self.total_tests - self.passed_tests,
                "pass_rate": pass_rate,
                "average_score": avg_score,
                "average_response_time": avg_response_time,
                "total_execution_time": total_time
            },
            "categories": {},
            "detailed_results": [
                {
                    "test_name": r.test_name,
                    "category": r.category,
                    "status": r.status.value,
                    "score": r.score,
                    "details": r.details,
                    "response_time": r.response_time
                }
                for r in self.test_results
            ]
        }
        
        # Category summaries
        for category, results in categories.items():
            category_pass_rate = sum(1 for r in results if r.status == TestStatus.PASS) / len(results) * 100
            category_avg_score = sum(r.score for r in results) / len(results)
            
            report["categories"][category] = {
                "total_tests": len(results),
                "pass_rate": category_pass_rate,
                "average_score": category_avg_score,
                "status": "PASS" if category_pass_rate >= 80 else "FAIL"
            }
        
        # Print summary
        logger.info("\n" + "=" * 60)
        logger.info("🎯 A.T.L.A.S. COMPREHENSIVE TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"📊 Overall Results:")
        logger.info(f"   Total Tests: {self.total_tests}")
        logger.info(f"   Passed: {self.passed_tests}")
        logger.info(f"   Failed: {self.total_tests - self.passed_tests}")
        logger.info(f"   Pass Rate: {pass_rate:.1f}%")
        logger.info(f"   Average Score: {avg_score:.1f}/10")
        logger.info(f"   Average Response Time: {avg_response_time:.2f}s")
        logger.info(f"   Total Execution Time: {total_time:.1f}s")
        
        logger.info(f"\n📋 Category Results:")
        for category, stats in report["categories"].items():
            status_emoji = "✅" if stats["status"] == "PASS" else "❌"
            logger.info(f"   {status_emoji} {category}: {stats['pass_rate']:.1f}% ({stats['average_score']:.1f}/10)")
        
        return report

# Main execution
async def main():
    """Run the comprehensive test suite"""
    test_suite = ATLASTestSuite()
    
    try:
        report = await test_suite.run_all_tests()
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"atlas_test_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"\n📄 Detailed report saved to: {report_file}")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Test suite execution failed: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    asyncio.run(main())
