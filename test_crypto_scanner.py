#!/usr/bin/env python3
"""
Test crypto scanner with real data
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append('.')

from strategies import scan_crypto_macd
from sp500_symbols import get_crypto_symbols

def test_crypto_scanner():
    """Test crypto MACD scanner with real symbols"""
    
    print("🧪 Testing Crypto MACD Scanner...")
    
    # Get crypto symbols
    crypto_symbols = get_crypto_symbols()
    print(f"📊 Testing with crypto symbols: {crypto_symbols[:3]}")
    
    try:
        # Run the scanner
        results = scan_crypto_macd(crypto_symbols[:3], fast=12, slow=26, signal=9)
        
        print(f"\n📈 Scanner Results:")
        print(f"Number of results: {len(results)}")
        
        for i, result in enumerate(results):
            print(f"\n{i+1}. {result.get('symbol', 'Unknown')}")
            print(f"   Algorithm: {result.get('algorithm', 'N/A')}")
            print(f"   Signal: {result.get('signal', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 'N/A')}")
            if 'setup_details' in result:
                print(f"   Details: {result['setup_details']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error running scanner: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    test_crypto_scanner()
