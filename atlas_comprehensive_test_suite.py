#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Test Suite
Automated testing covering all 25+ features with specific validation criteria and success metrics
"""

import asyncio
import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import concurrent.futures

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """Individual test result"""
    test_name: str
    category: str
    passed: bool
    response_time: float
    error_message: Optional[str] = None
    response_data: Optional[Dict] = None
    validation_details: Optional[Dict] = None

class AtlasComprehensiveTestSuite:
    """Comprehensive test suite for all A.T.L.A.S. features"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.logger = logger
        self.results: List[TestResult] = []
        
        # Test configuration
        self.timeout = 30
        self.max_retries = 2
        
        # Success criteria
        self.min_pass_rate = 0.80  # 80% minimum pass rate
        self.max_response_time = 10.0  # 10 seconds max response time
        self.min_confidence = 0.6  # 60% minimum confidence for trading responses
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all comprehensive tests"""
        print("🚀 STARTING A.T.L.A.S. COMPREHENSIVE TEST SUITE")
        print("=" * 80)
        
        start_time = time.time()
        
        # Test categories
        test_categories = [
            ("API Health", self.test_api_health),
            ("Conversational AI", self.test_conversational_ai),
            ("Trading Analysis", self.test_trading_analysis),
            ("TTM Squeeze Detection", self.test_ttm_squeeze),
            ("Options Trading", self.test_options_trading),
            ("Portfolio Optimization", self.test_portfolio_optimization),
            ("Market Data Integration", self.test_market_data),
            ("Real-time Scanning", self.test_realtime_scanning),
            ("Risk Management", self.test_risk_management),
            ("Web Interface", self.test_web_interface),
            ("Performance & Reliability", self.test_performance)
        ]
        
        # Run tests sequentially to avoid overwhelming the server
        for category_name, test_function in test_categories:
            print(f"\n📊 Testing {category_name}...")
            print("-" * 60)
            
            try:
                await test_function()
            except Exception as e:
                self.logger.error(f"Error in {category_name} tests: {e}")
                self.results.append(TestResult(
                    test_name=f"{category_name} - Critical Error",
                    category=category_name,
                    passed=False,
                    response_time=0.0,
                    error_message=str(e)
                ))
            
            # Brief pause between categories
            await asyncio.sleep(1)
        
        total_time = time.time() - start_time
        
        # Generate comprehensive report
        report = self.generate_comprehensive_report(total_time)
        
        return report
    
    async def test_api_health(self):
        """Test basic API health and connectivity"""
        tests = [
            ("Health Check", "GET", "/api/v1/health", None),
            ("Chat API Availability", "POST", "/api/v1/chat", {"message": "test", "session_id": "health-test"}),
            ("Quote API", "GET", "/api/v1/quote/AAPL", None),
            ("Scanner API", "GET", "/api/v1/scan", None)
        ]
        
        for test_name, method, endpoint, payload in tests:
            result = await self.make_api_call(test_name, "API Health", method, endpoint, payload)
            self.results.append(result)
    
    async def test_conversational_ai(self):
        """Test conversational AI capabilities"""
        tests = [
            {
                "name": "Simple Greeting",
                "message": "Hello",
                "expect_type": "greeting",
                "expect_content": ["hello", "hi", "welcome", "atlas", "predicto"]
            },
            {
                "name": "Capabilities Inquiry",
                "message": "What can you help me with?",
                "expect_type": "capabilities",
                "expect_content": ["trading", "analysis", "market", "help"]
            },
            {
                "name": "Context Awareness",
                "message": "Tell me more about that",
                "expect_type": "conversational",
                "expect_content": ["more", "information", "details"]
            },
            {
                "name": "Natural Language Understanding",
                "message": "I'm new to trading, can you guide me?",
                "expect_type": "educational",
                "expect_content": ["beginner", "guide", "learn", "start"]
            }
        ]
        
        for test in tests:
            result = await self.test_chat_message(
                test["name"], "Conversational AI", test["message"],
                test["expect_type"], test["expect_content"]
            )
            self.results.append(result)
    
    async def test_trading_analysis(self):
        """Test trading analysis and 6-point guru format"""
        tests = [
            {
                "name": "Stock Analysis - AAPL",
                "message": "Analyze AAPL for trading",
                "expect_type": "guru_analysis",
                "expect_6_point": True,
                "expect_content": ["aapl", "trade", "price", "target", "stop"]
            },
            {
                "name": "Stock Analysis - TSLA",
                "message": "Should I buy Tesla stock?",
                "expect_type": "guru_analysis", 
                "expect_6_point": True,
                "expect_content": ["tesla", "tsla", "buy", "recommendation"]
            },
            {
                "name": "Multiple Stock Comparison",
                "message": "Compare AAPL vs MSFT for investment",
                "expect_type": "guru_analysis",
                "expect_6_point": True,
                "expect_content": ["aapl", "msft", "compare", "investment"]
            },
            {
                "name": "Sector Analysis",
                "message": "What's the outlook for tech stocks?",
                "expect_type": "guru_analysis",
                "expect_6_point": False,
                "expect_content": ["tech", "technology", "sector", "outlook"]
            }
        ]
        
        for test in tests:
            result = await self.test_trading_message(
                test["name"], "Trading Analysis", test["message"],
                test["expect_type"], test["expect_content"], test.get("expect_6_point", False)
            )
            self.results.append(result)
    
    async def test_ttm_squeeze(self):
        """Test TTM Squeeze pattern detection"""
        tests = [
            {
                "name": "TTM Squeeze Scan",
                "message": "Scan for TTM Squeeze signals",
                "expect_content": ["ttm", "squeeze", "signal", "momentum"]
            },
            {
                "name": "Symbol-Specific TTM",
                "message": "Check NVDA for TTM Squeeze patterns",
                "expect_content": ["nvda", "ttm", "squeeze", "pattern"]
            },
            {
                "name": "TTM Squeeze Explanation",
                "message": "Explain TTM Squeeze indicator",
                "expect_content": ["ttm", "squeeze", "bollinger", "keltner", "momentum"]
            }
        ]
        
        for test in tests:
            result = await self.test_chat_message(
                test["name"], "TTM Squeeze", test["message"],
                "guru_analysis", test["expect_content"]
            )
            self.results.append(result)
    
    async def test_options_trading(self):
        """Test options trading capabilities"""
        tests = [
            {
                "name": "Options Strategy Recommendation",
                "message": "What options strategies work for AAPL?",
                "expect_content": ["options", "strategy", "call", "put", "greeks"]
            },
            {
                "name": "Specific Options Analysis",
                "message": "Analyze TSLA call options expiring next month",
                "expect_content": ["tsla", "call", "options", "expiration", "delta"]
            },
            {
                "name": "Options Education",
                "message": "Explain covered call strategy",
                "expect_content": ["covered call", "strategy", "premium", "income"]
            }
        ]
        
        for test in tests:
            result = await self.test_chat_message(
                test["name"], "Options Trading", test["message"],
                "guru_analysis", test["expect_content"]
            )
            self.results.append(result)
    
    async def test_portfolio_optimization(self):
        """Test portfolio optimization features"""
        tests = [
            {
                "name": "Portfolio Optimization",
                "message": "Optimize my portfolio with AAPL, MSFT, GOOGL",
                "expect_content": ["portfolio", "optimization", "allocation", "risk"]
            },
            {
                "name": "Risk Assessment",
                "message": "What's the risk of my current portfolio?",
                "expect_content": ["risk", "assessment", "portfolio", "volatility"]
            },
            {
                "name": "Diversification Advice",
                "message": "How should I diversify my tech-heavy portfolio?",
                "expect_content": ["diversification", "portfolio", "sectors", "allocation"]
            }
        ]
        
        for test in tests:
            result = await self.test_chat_message(
                test["name"], "Portfolio Optimization", test["message"],
                "guru_analysis", test["expect_content"]
            )
            self.results.append(result)
    
    async def test_market_data(self):
        """Test market data integration"""
        symbols = ["AAPL", "TSLA", "MSFT", "GOOGL", "NVDA"]
        
        for symbol in symbols:
            result = await self.make_api_call(
                f"Quote Data - {symbol}", "Market Data", "GET", f"/api/v1/quote/{symbol}", None
            )
            self.results.append(result)
    
    async def test_realtime_scanning(self):
        """Test real-time market scanning"""
        result = await self.make_api_call(
            "Market Scanner", "Real-time Scanning", "GET", "/api/v1/scan", None
        )
        self.results.append(result)
        
        # Test conversational scanning
        scan_result = await self.test_chat_message(
            "Conversational Market Scan", "Real-time Scanning",
            "Show me today's best trading opportunities",
            "guru_analysis", ["opportunities", "trading", "signals"]
        )
        self.results.append(scan_result)
    
    async def test_risk_management(self):
        """Test risk management features"""
        tests = [
            {
                "name": "Position Sizing",
                "message": "How much should I invest in AAPL?",
                "expect_content": ["position", "size", "risk", "investment"]
            },
            {
                "name": "Stop Loss Recommendation",
                "message": "Where should I set my stop loss for TSLA?",
                "expect_content": ["stop", "loss", "risk", "management"]
            }
        ]
        
        for test in tests:
            result = await self.test_chat_message(
                test["name"], "Risk Management", test["message"],
                "guru_analysis", test["expect_content"]
            )
            self.results.append(result)
    
    async def test_web_interface(self):
        """Test web interface functionality"""
        # Test if HTML interface loads
        try:
            import os
            html_path = "atlas_interface.html"
            if os.path.exists(html_path):
                with open(html_path, 'r') as f:
                    html_content = f.read()
                
                # Check for key elements
                has_chat_interface = 'chat-messages' in html_content
                has_api_calls = 'api/v1/chat' in html_content
                has_styling = 'background:' in html_content or 'color:' in html_content
                
                self.results.append(TestResult(
                    test_name="HTML Interface Structure",
                    category="Web Interface",
                    passed=has_chat_interface and has_api_calls and has_styling,
                    response_time=0.0,
                    validation_details={
                        "has_chat_interface": has_chat_interface,
                        "has_api_calls": has_api_calls,
                        "has_styling": has_styling
                    }
                ))
            else:
                self.results.append(TestResult(
                    test_name="HTML Interface Exists",
                    category="Web Interface",
                    passed=False,
                    response_time=0.0,
                    error_message="atlas_interface.html not found"
                ))
                
        except Exception as e:
            self.results.append(TestResult(
                test_name="Web Interface Test",
                category="Web Interface",
                passed=False,
                response_time=0.0,
                error_message=str(e)
            ))
    
    async def test_performance(self):
        """Test performance and reliability"""
        # Concurrent request test
        concurrent_tests = []
        for i in range(5):
            concurrent_tests.append(
                self.test_chat_message(
                    f"Concurrent Test {i+1}", "Performance",
                    f"Analyze stock performance test {i+1}",
                    "guru_analysis", ["analysis", "stock"]
                )
            )
        
        # Run concurrent tests
        start_time = time.time()
        concurrent_results = await asyncio.gather(*concurrent_tests, return_exceptions=True)
        concurrent_time = time.time() - start_time
        
        # Process results
        successful_concurrent = sum(1 for r in concurrent_results if isinstance(r, TestResult) and r.passed)
        
        self.results.append(TestResult(
            test_name="Concurrent Request Handling",
            category="Performance",
            passed=successful_concurrent >= 3,  # At least 3 out of 5 should succeed
            response_time=concurrent_time,
            validation_details={
                "successful_requests": successful_concurrent,
                "total_requests": 5,
                "total_time": concurrent_time
            }
        ))
        
        # Add individual concurrent results
        for i, result in enumerate(concurrent_results):
            if isinstance(result, TestResult):
                self.results.append(result)
    
    async def make_api_call(self, test_name: str, category: str, method: str, 
                           endpoint: str, payload: Optional[Dict]) -> TestResult:
        """Make API call and validate response"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{endpoint}"
            
            if method == "GET":
                response = requests.get(url, timeout=self.timeout)
            elif method == "POST":
                response = requests.post(url, json=payload, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = time.time() - start_time
            
            # Basic validation
            passed = response.status_code == 200 and response_time <= self.max_response_time
            
            response_data = None
            if response.status_code == 200:
                try:
                    response_data = response.json()
                except:
                    response_data = {"raw_response": response.text[:200]}
            
            return TestResult(
                test_name=test_name,
                category=category,
                passed=passed,
                response_time=response_time,
                error_message=None if passed else f"Status: {response.status_code}",
                response_data=response_data
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                test_name=test_name,
                category=category,
                passed=False,
                response_time=response_time,
                error_message=str(e)
            )
    
    async def test_chat_message(self, test_name: str, category: str, message: str,
                               expect_type: str, expect_content: List[str]) -> TestResult:
        """Test chat message with content validation"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/chat",
                json={
                    "message": message,
                    "session_id": f"test-{int(time.time())}-{hash(test_name) % 1000}",
                    "user_id": "test_suite"
                },
                timeout=self.timeout
            )
            
            response_time = time.time() - start_time
            
            if response.status_code != 200:
                return TestResult(
                    test_name=test_name,
                    category=category,
                    passed=False,
                    response_time=response_time,
                    error_message=f"HTTP {response.status_code}: {response.text}"
                )
            
            data = response.json()
            response_text = data.get('response', '').lower()
            response_type = data.get('type', 'unknown')
            confidence = data.get('confidence', 0.0)
            
            # Content validation
            content_matches = sum(1 for term in expect_content if term.lower() in response_text)
            content_score = content_matches / len(expect_content) if expect_content else 1.0
            
            # Overall validation
            type_match = expect_type in response_type or response_type in expect_type
            confidence_ok = confidence >= self.min_confidence if expect_type == "guru_analysis" else True
            content_ok = content_score >= 0.5  # At least 50% of expected content
            response_time_ok = response_time <= self.max_response_time
            
            passed = type_match and confidence_ok and content_ok and response_time_ok
            
            return TestResult(
                test_name=test_name,
                category=category,
                passed=passed,
                response_time=response_time,
                response_data=data,
                validation_details={
                    "type_match": type_match,
                    "confidence_ok": confidence_ok,
                    "content_score": content_score,
                    "response_time_ok": response_time_ok,
                    "expected_type": expect_type,
                    "actual_type": response_type,
                    "confidence": confidence,
                    "content_matches": f"{content_matches}/{len(expect_content)}"
                }
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                test_name=test_name,
                category=category,
                passed=False,
                response_time=response_time,
                error_message=str(e)
            )

    def generate_comprehensive_report(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        print("\n" + "=" * 80)
        print("📋 A.T.L.A.S. COMPREHENSIVE TEST REPORT")
        print("=" * 80)

        # Overall statistics
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        failed_tests = total_tests - passed_tests
        pass_rate = passed_tests / total_tests if total_tests > 0 else 0.0

        print(f"📊 OVERALL RESULTS:")
        print(f"   • Total Tests: {total_tests}")
        print(f"   • Passed: {passed_tests}")
        print(f"   • Failed: {failed_tests}")
        print(f"   • Pass Rate: {pass_rate:.1%}")
        print(f"   • Total Time: {total_time:.2f}s")

        # Category breakdown
        categories = {}
        for result in self.results:
            if result.category not in categories:
                categories[result.category] = {'total': 0, 'passed': 0, 'times': []}
            categories[result.category]['total'] += 1
            if result.passed:
                categories[result.category]['passed'] += 1
            categories[result.category]['times'].append(result.response_time)

        print(f"\n📈 CATEGORY BREAKDOWN:")
        for category, stats in categories.items():
            cat_pass_rate = stats['passed'] / stats['total'] if stats['total'] > 0 else 0.0
            avg_time = sum(stats['times']) / len(stats['times']) if stats['times'] else 0.0
            status = "✅" if cat_pass_rate >= self.min_pass_rate else "❌"
            print(f"   {status} {category}: {stats['passed']}/{stats['total']} ({cat_pass_rate:.1%}) - Avg: {avg_time:.2f}s")

        # Performance analysis
        response_times = [r.response_time for r in self.results if r.response_time > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0.0
        max_response_time = max(response_times) if response_times else 0.0
        slow_tests = sum(1 for t in response_times if t > self.max_response_time) if response_times else 0

        print(f"\n⚡ PERFORMANCE ANALYSIS:")
        print(f"   • Average Response Time: {avg_response_time:.2f}s")
        print(f"   • Maximum Response Time: {max_response_time:.2f}s")
        print(f"   • Slow Tests (>{self.max_response_time}s): {slow_tests}")

        # Success assessment
        print(f"\n🎯 SUCCESS ASSESSMENT:")
        if pass_rate >= 0.95:
            print("   🎉 EXCELLENT: A.T.L.A.S. is performing exceptionally well!")
            assessment = "EXCELLENT"
        elif pass_rate >= self.min_pass_rate:
            print("   ✅ GOOD: A.T.L.A.S. is performing well with minor issues")
            assessment = "GOOD"
        elif pass_rate >= 0.60:
            print("   ⚠️ NEEDS_IMPROVEMENT: A.T.L.A.S. has significant issues")
            assessment = "NEEDS_IMPROVEMENT"
        else:
            print("   ❌ CRITICAL: A.T.L.A.S. has major system failures")
            assessment = "CRITICAL"

        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"atlas_test_report_{timestamp}.json"

        detailed_report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'pass_rate': pass_rate,
                'total_time': total_time,
                'assessment': assessment
            },
            'categories': categories,
            'performance': {
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'slow_tests': slow_tests
            }
        }

        with open(report_file, 'w') as f:
            json.dump(detailed_report, f, indent=2, default=str)

        print(f"\n📄 Detailed report saved to: {report_file}")
        return detailed_report

async def main():
    """Main test execution"""
    print("🚀 Initializing A.T.L.A.S. Comprehensive Test Suite...")

    # Check if server is running
    try:
        response = requests.get("http://localhost:8080/api/v1/health", timeout=5)
        if response.status_code != 200:
            print("❌ A.T.L.A.S. server is not responding properly")
            return
    except:
        print("❌ Cannot connect to A.T.L.A.S. server")
        print("   Please ensure the server is running on http://localhost:8080")
        return

    print("✅ A.T.L.A.S. server is running")

    # Run comprehensive tests
    test_suite = AtlasComprehensiveTestSuite()
    report = await test_suite.run_all_tests()

    # Final summary
    print(f"\n🎯 FINAL SUMMARY:")
    print(f"   Assessment: {report['summary']['assessment']}")
    print(f"   Pass Rate: {report['summary']['pass_rate']:.1%}")

    if report['summary']['pass_rate'] >= 0.80:
        print(f"   🎉 A.T.L.A.S. IS READY FOR PRODUCTION!")
    else:
        print(f"   ⚠️ A.T.L.A.S. needs improvements before production deployment")

if __name__ == "__main__":
    asyncio.run(main())
