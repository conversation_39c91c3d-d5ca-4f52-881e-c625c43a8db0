#!/usr/bin/env python3
"""
Test script to validate Lee Method fixes without starting full server
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '1_main_chat_engine'))

def test_lee_method_detection():
    """Test the Lee Method detection logic"""
    print("🔧 TESTING LEE METHOD DETECTION LOGIC")
    print("=" * 60)
    
    try:
        # Import the predicto engine
        from atlas_predicto_engine import AtlasPredictoEngine
        
        # Create engine instance
        engine = AtlasPredictoEngine()
        
        # Test messages
        test_messages = [
            "What are the 3 criteria for the Lee Method?",
            "Explain the Lee Method",
            "What is the Lee Method?",
            "Lee Method criteria",
            "Tell me about Lee Method pattern detection"
        ]
        
        print("Testing Lee Method detection in messages:")
        print("-" * 40)
        
        for message in test_messages:
            print(f"\n📝 Testing: '{message}'")
            
            # Test symbol extraction
            symbols = engine._extract_symbols_from_message(message)
            print(f"   Symbols extracted: {symbols}")
            
            # Test conversation intent
            intent = engine._analyze_conversation_intent(message)
            print(f"   Intent type: {intent.get('intent_type', 'unknown')}")
            print(f"   Requires stock analysis: {intent.get('requires_stock_analysis', False)}")
            print(f"   Required capabilities: {intent.get('required_capabilities', [])}")
            
            # Check if Lee Method is properly detected
            if intent.get('intent_type') == 'lee_method_query':
                print("   ✅ SUCCESS: Lee Method query detected!")
            elif 'LEE' in symbols:
                print("   ❌ ISSUE: 'LEE' detected as stock symbol!")
            else:
                print("   ⚠️ UNCLEAR: Intent not clearly identified")
        
        print("\n" + "=" * 60)
        print("🎯 LEE METHOD DETECTION TEST COMPLETE!")
        
        # Test the Lee Method handler
        print("\n🔧 TESTING LEE METHOD HANDLER")
        print("-" * 40)
        
        # Test the handler method exists
        if hasattr(engine, '_handle_lee_method_query'):
            print("✅ Lee Method handler exists")
            
            # Test a simple call (this will fail without full initialization, but we can check the method)
            try:
                import asyncio
                result = asyncio.run(engine._handle_lee_method_query("What is the Lee Method?"))
                print(f"✅ Handler response type: {result.type}")
                print(f"✅ Handler confidence: {result.confidence}")
                print(f"📝 Response preview: {result.response[:100]}...")
            except Exception as e:
                print(f"⚠️ Handler test failed (expected): {e}")
        else:
            print("❌ Lee Method handler missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_lee_method_detection()
    if success:
        print("\n✅ Lee Method fixes appear to be working!")
    else:
        print("\n❌ Lee Method fixes need more work!")
