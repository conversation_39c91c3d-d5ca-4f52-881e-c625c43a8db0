#!/usr/bin/env python3
"""
Test script to check market data retrieval
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append('.')

from strategies import get_market_data
from sp500_symbols import get_crypto_symbols, get_options_symbols, get_sp500_symbols

def test_market_data():
    """Test market data retrieval for different asset types"""
    
    print("🧪 Testing Market Data Retrieval...")
    print(f"APCA_API_KEY_ID: {os.getenv('APCA_API_KEY_ID', 'NOT SET')}")
    print(f"APCA_API_SECRET_KEY: {'SET' if os.getenv('APCA_API_SECRET_KEY') else 'NOT SET'}")
    print()
    
    # Test stock data
    print("📈 Testing Stock Data:")
    stock_symbols = ["AAPL", "MSFT", "GOOGL"]
    for symbol in stock_symbols:
        try:
            df = get_market_data(symbol, timeframe="1Day", limit=10)
            if df is not None and len(df) > 0:
                print(f"✅ {symbol}: Got {len(df)} bars, latest close: ${df['close'].iloc[-1]:.2f}")
            else:
                print(f"❌ {symbol}: No data returned")
        except Exception as e:
            print(f"❌ {symbol}: Error - {str(e)}")
    
    print()
    
    # Test crypto data
    print("₿ Testing Crypto Data:")
    crypto_symbols = get_crypto_symbols()[:3]  # Test first 3
    for symbol in crypto_symbols:
        try:
            df = get_market_data(symbol, timeframe="1Day", limit=10)
            if df is not None and len(df) > 0:
                print(f"✅ {symbol}: Got {len(df)} bars, latest close: ${df['close'].iloc[-1]:.2f}")
            else:
                print(f"❌ {symbol}: No data returned")
        except Exception as e:
            print(f"❌ {symbol}: Error - {str(e)}")
    
    print()
    
    # Test options symbols
    print("📊 Testing Options Symbols:")
    options_symbols = get_options_symbols()[:3]  # Test first 3
    for symbol in options_symbols:
        try:
            df = get_market_data(symbol, timeframe="1Day", limit=10)
            if df is not None and len(df) > 0:
                print(f"✅ {symbol}: Got {len(df)} bars, latest close: ${df['close'].iloc[-1]:.2f}")
            else:
                print(f"❌ {symbol}: No data returned")
        except Exception as e:
            print(f"❌ {symbol}: Error - {str(e)}")

if __name__ == "__main__":
    test_market_data()
