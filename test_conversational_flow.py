#!/usr/bin/env python3
"""
Test True Conversational Flow
Validate that A.T.L.A.S. now provides context-aware, flowing conversations
"""

import requests
import json
import time
from datetime import datetime

def test_conversation_flow():
    """Test multi-turn conversation with context awareness"""
    base_url = "http://localhost:8080"
    session_id = f"conv-test-{int(time.time())}"
    
    print("🧪 Testing True Conversational Flow")
    print("=" * 50)
    
    # Test conversation sequence
    conversation = [
        "Hello, I'm new to trading",
        "What can you help me with?",
        "I'm interested in Apple stock",
        "Should I buy AAPL now?",
        "What about Tesla?",
        "Thanks for the help"
    ]
    
    responses = []
    
    for i, message in enumerate(conversation, 1):
        print(f"\n{i}. User: {message}")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": message,
                    "session_id": session_id,
                    "conversation_history": responses  # Pass history for context
                },
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                print(f"   A.T.L.A.S.: {response_text[:200]}...")
                print(f"   Response time: {response_time:.2f}s")
                print(f"   Type: {data.get('type', 'unknown')}")
                print(f"   Confidence: {data.get('confidence', 0):.2f}")
                
                # Store for context
                responses.append({
                    "user": message,
                    "assistant": response_text,
                    "timestamp": datetime.now().isoformat()
                })
                
                # Analyze response quality
                is_contextual = analyze_contextual_awareness(message, response_text, responses)
                is_natural = analyze_natural_flow(response_text)
                has_branding = "A.T.L.A.S" in response_text and "Predicto" in response_text
                
                print(f"   Contextual: {'✅' if is_contextual else '❌'}")
                print(f"   Natural: {'✅' if is_natural else '❌'}")
                print(f"   Branding: {'✅' if has_branding else '❌'}")
                
            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Small delay between messages
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print("🎯 Conversational Flow Test Complete")
    
    # Test specific scenarios
    test_context_awareness(base_url, session_id)

def analyze_contextual_awareness(message, response, conversation_history):
    """Check if response shows awareness of conversation context"""
    if len(conversation_history) <= 1:
        return True  # First message doesn't need context
    
    # Check for references to previous topics
    previous_topics = []
    for exchange in conversation_history[:-1]:  # Exclude current
        if "apple" in exchange["user"].lower() or "aapl" in exchange["user"].lower():
            previous_topics.append("apple")
        if "tesla" in exchange["user"].lower() or "tsla" in exchange["user"].lower():
            previous_topics.append("tesla")
    
    # Response should reference previous topics when relevant
    if "tesla" in message.lower() and "apple" in previous_topics:
        return "apple" in response.lower() or "aapl" in response.lower() or "compared" in response.lower()
    
    return True  # Default to true for other cases

def analyze_natural_flow(response):
    """Check if response feels natural and conversational"""
    # Check for natural conversation markers
    natural_markers = [
        "i can", "i'll", "let me", "sure", "absolutely", "of course",
        "great question", "that's", "you're", "i'd be happy",
        "here's what", "let's", "i think", "i'd recommend"
    ]
    
    # Check against overly robotic patterns
    robotic_patterns = [
        "as an ai", "i cannot", "i am not able", "i don't have the ability",
        "please note that", "it is important to note", "disclaimer:"
    ]
    
    response_lower = response.lower()
    
    has_natural = any(marker in response_lower for marker in natural_markers)
    has_robotic = any(pattern in response_lower for pattern in robotic_patterns)
    
    return has_natural and not has_robotic

def test_context_awareness(base_url, session_id):
    """Test specific context awareness scenarios"""
    print("\n🔍 Testing Context Awareness")
    print("-" * 30)
    
    # Test follow-up questions
    test_cases = [
        {
            "setup": "Analyze AAPL for trading",
            "followup": "What about the risks?",
            "expect_context": "should reference AAPL analysis"
        },
        {
            "setup": "I want to make $500 this week",
            "followup": "Is that realistic?",
            "expect_context": "should reference the $500 goal"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. Context Test: {case['setup']}")
        
        # Send setup message
        try:
            response1 = requests.post(
                f"{base_url}/api/v1/chat",
                json={"message": case["setup"], "session_id": f"{session_id}-ctx{i}"},
                timeout=20
            )
            
            if response1.status_code == 200:
                print(f"   Setup response: {response1.json().get('response', '')[:100]}...")
                
                # Send follow-up
                response2 = requests.post(
                    f"{base_url}/api/v1/chat",
                    json={"message": case["followup"], "session_id": f"{session_id}-ctx{i}"},
                    timeout=20
                )
                
                if response2.status_code == 200:
                    followup_text = response2.json().get('response', '')
                    print(f"   Follow-up: {followup_text[:100]}...")
                    print(f"   Expected: {case['expect_context']}")
                    
                    # Check if follow-up shows context awareness
                    if "aapl" in case["setup"].lower() and "aapl" in followup_text.lower():
                        print("   ✅ Context maintained")
                    elif "$500" in case["setup"] and ("500" in followup_text or "goal" in followup_text.lower()):
                        print("   ✅ Context maintained")
                    else:
                        print("   ❌ Context not maintained")
                else:
                    print(f"   ❌ Follow-up failed: {response2.status_code}")
            else:
                print(f"   ❌ Setup failed: {response1.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    test_conversation_flow()
