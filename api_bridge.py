import os
import aiohttp
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from dotenv import load_dotenv
import logging
from datetime import datetime

# Load environment variables
load_dotenv()

ALP_BASE = os.getenv("ALPACA_API_BASE", "https://paper-api.alpaca.markets")
ALP_KEY = os.getenv("APCA_API_KEY_ID")
ALP_SECRET = os.getenv("APCA_API_SECRET_KEY")
FMP_KEY = os.getenv("FMP_KEY")

# Configure structured JSON logger
logger = logging.getLogger("atlas.api_bridge")
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
formatter = logging.Formatter('{"timestamp": "%(asctime)s", "level": "%(levelname)s", "function": "%(funcName)s", "message": %(message)s}')
handler.setFormatter(formatter)
if not logger.hasHandlers():
    logger.addHandler(handler)

# Retry config
RETRY_CONFIG = dict(
    stop=stop_after_attempt(5),
    wait=wait_exponential(multiplier=1, min=2, max=10),
    retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError)),
    reraise=True
)

async def log_json(event, **kwargs):
    log_entry = {"event": event, **kwargs, "timestamp": datetime.utcnow().isoformat()}
    logger.info(str(log_entry))

@retry(**RETRY_CONFIG)
async def call_alpaca_api(path, method="GET", query_params=None, body=None, apca_api_key_id=None, apca_api_secret_key=None):
    """Async, robust, generic Alpaca API bridge."""
    url = f"{ALP_BASE}/v2/{path.lstrip('/')}"
    headers = {
        "APCA-API-KEY-ID": apca_api_key_id or ALP_KEY,
        "APCA-API-SECRET-KEY": apca_api_secret_key or ALP_SECRET,
        "Content-Type": "application/json",
    }
    if not headers["APCA-API-KEY-ID"] or not headers["APCA-API-SECRET-KEY"]:
        await log_json("alpaca.missing_credentials", path=path)
        raise ValueError("Missing Alpaca credentials.")
    await log_json("alpaca.request", method=method, url=url, params=query_params, body=body)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.request(method, url, headers=headers, params=query_params, json=body, timeout=30) as resp:
                data = await resp.json(content_type=None)
                await log_json("alpaca.response", status=resp.status, data=data)
                return {"status_code": resp.status, "data": data, "success": resp.status < 400}
        except Exception as e:
            await log_json("alpaca.error", error=str(e))
            raise

@retry(**RETRY_CONFIG)
async def call_fmp_api(endpoint, query_params=None):
    """Async, robust, generic FMP API bridge."""
    if not FMP_KEY:
        await log_json("fmp.missing_credentials", endpoint=endpoint)
        raise ValueError("Missing FMP_KEY.")
    params = (query_params or {}).copy()
    params["apikey"] = FMP_KEY
    if endpoint.startswith("/"):
        endpoint = endpoint[1:]
    if not endpoint.startswith("v"):
        url = f"https://financialmodelingprep.com/api/v3/{endpoint}"
    else:
        url = f"https://financialmodelingprep.com/api/{endpoint}"
    await log_json("fmp.request", url=url, params=params)
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(url, params=params, timeout=30) as resp:
                data = await resp.json(content_type=None)
                await log_json("fmp.response", status=resp.status, data=data)
                return {"status_code": resp.status, "data": data, "success": resp.status < 400}
        except Exception as e:
            await log_json("fmp.error", error=str(e))
            raise

async def check_alpaca_connection():
    try:
        result = await call_alpaca_api("account")
        return result["success"]
    except Exception as e:
        await log_json("alpaca.health_check_failed", error=str(e))
        return False

async def check_fmp_connection():
    try:
        result = await call_fmp_api("profile/AAPL")
        return result["success"]
    except Exception as e:
        await log_json("fmp.health_check_failed", error=str(e))
        return False 