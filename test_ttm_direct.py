"""
Direct test of TTM scanner to debug issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategies import scan_ttm_squeeze

def test_ttm_scanner():
    print("Testing TTM Scanner directly...")
    
    try:
        # Test with just a few symbols
        test_symbols = ['AAPL', 'MSFT', 'GOOGL']
        results = scan_ttm_squeeze(symbols=test_symbols, limit=5)
        
        print(f"\nResults: {len(results)} signals found")
        
        if results:
            for i, signal in enumerate(results[:3]):
                print(f"\n{i+1}. {signal['symbol']} - {signal['algorithm']}")
                print(f"   Entry: ${signal['entry_price']}")
                print(f"   Confidence: {signal['confidence']}")
                if 'setup_details' in signal:
                    print(f"   Pattern: {signal['setup_details'].get('pattern', 'N/A')}")
        else:
            print("No signals found")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ttm_scanner()
