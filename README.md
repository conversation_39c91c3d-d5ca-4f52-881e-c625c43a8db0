# ATLAS Trading Assistant v2

## 🚀 Quick Start

1. **Start ATLAS**: Double-click `ATLAS_LAUNCHER.bat`
2. **Open Browser**: Go to http://localhost:8000
3. **Start Trading**: Use the chat interface or scanner buttons

## 📋 Features

### ✅ Working Features
- **TTM Squeeze Scanner** - Finds momentum shifts with 4+ red bars → yellow bar pattern
- **All Stock Scanners** - MA Crossover, Bollinger, RSI, Volume, Donchian
- **Options Scanners** - Iron Condor, Straddles, Spreads, etc.
- **Crypto Scanners** - MACD, RSI, On-chain analysis
- **Fundamental Scanners** - Insider buying, Analyst upgrades
- **AI Chat Interface** - Natural language trading assistant
- **Real-time Account Info** - Shows your actual Alpaca account balance
- **Trade Execution** - Can place real trades (with confirmation)

### 🔧 Configuration

**API keys are now hardcoded in the application!** No need to create a `.env` file.

The following API keys are already configured:
- ✅ **Alpaca API** - Paper trading account ready to use
- ✅ **OpenAI API** - ChatGPT integration enabled
- ❌ **Financial Modeling Prep** - Not configured (optional)

To change API keys, edit:
- `comprehensive_api.py` - Lines 32-34 for Alpaca keys
- `atlas_core.py` - Line 63 for OpenAI key

### 📊 TTM Squeeze Scanner

The TTM Squeeze scanner looks for:
- **Pattern**: 4+ consecutive red bars (negative momentum) followed by first yellow bar
- **EMA Trend**: Rising EMAs (8 > 21 > 50) for bullish confirmation
- **Multiple Timeframes**: Scans 5min, 15min, 30min, 1hr, and daily
- **Auto-scan**: Can run every 5-15 minutes across S&P 500 + $100B+ stocks

To start auto-scanner:
1. Run `START_TTM_SCANNER.bat`
2. Results saved to `ttm_scan_results/` folder

### 🛠️ Troubleshooting

**"Alpaca API error 404"**
- This should be fixed now! The API keys are hardcoded
- If you still see this error, the Alpaca API might be down
- Try again in a few minutes

**"Internal Server Error" on scanners**
- The API keys are already configured
- If you see this error, check your internet connection
- The Alpaca API might be temporarily unavailable

**"No signals found"**
- Try different scanners or timeframes
- Market might be closed (stocks only trade 9:30 AM - 4 PM ET)
- Crypto scanners work 24/7

### 📁 File Structure

```
ATLAS_v2/
├── atlas_core.py          # Core trading logic (OpenAI key here)
├── atlas_web.py           # FastAPI server
├── strategies.py          # All trading strategies
├── comprehensive_api.py   # API integrations (Alpaca keys here)
├── index.html            # Web interface
├── ATLAS_LAUNCHER.bat    # Start ATLAS
└── START_TTM_SCANNER.bat # Start auto-scanner
```

### 💡 Tips

1. **Start Small**: Test with paper trading first
2. **Use Stop Losses**: Always set risk limits
3. **Diversify**: Use basket mode for multiple small trades
4. **Monitor**: Check positions regularly
5. **Learn**: Each scanner has different market conditions

### 🤖 AI Commands

Ask ATLAS anything:
- "Find me a trade to make $50"
- "What's the latest on Trump/Elon news?"
- "Show me insider trading activity"
- "Scan for TTM squeeze setups"
- "What are analysts saying about AAPL?"

### ⚠️ Disclaimer

This is a trading tool. Trading involves risk. Always:
- Do your own research
- Never invest more than you can afford to lose
- Use stop losses
- Start with paper trading

## 🆘 Support

For issues or questions:
1. Check the logs in terminal
2. API keys are already configured - no setup needed!
3. Ensure market is open for stock trading (9:30 AM - 4 PM ET)
4. Try different scanners or symbols
