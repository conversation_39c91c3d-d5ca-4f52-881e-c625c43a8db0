#!/usr/bin/env python3
"""
A.T.L.A.S. Web Search Engine
Provides real-time market news, earnings reports, and financial data research capabilities
"""

import asyncio
import logging
import aiohttp
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import quote_plus
import feedparser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Web search result with financial context"""
    title: str
    url: str
    snippet: str
    source: str
    timestamp: datetime
    relevance_score: float
    sentiment: str  # 'positive', 'negative', 'neutral'
    symbols_mentioned: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'title': self.title,
            'url': self.url,
            'snippet': self.snippet,
            'source': self.source,
            'timestamp': self.timestamp.isoformat(),
            'relevance_score': self.relevance_score,
            'sentiment': self.sentiment,
            'symbols_mentioned': self.symbols_mentioned
        }

@dataclass
class NewsAnalysis:
    """Comprehensive news analysis for trading decisions"""
    symbol: str
    overall_sentiment: str
    sentiment_score: float  # -1.0 to 1.0
    key_headlines: List[str]
    recent_news: List[SearchResult]
    earnings_info: Optional[Dict[str, Any]]
    analyst_ratings: List[Dict[str, Any]]
    market_impact: str  # 'bullish', 'bearish', 'neutral'
    confidence: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'overall_sentiment': self.overall_sentiment,
            'sentiment_score': self.sentiment_score,
            'key_headlines': self.key_headlines,
            'recent_news': [news.to_dict() for news in self.recent_news],
            'earnings_info': self.earnings_info,
            'analyst_ratings': self.analyst_ratings,
            'market_impact': self.market_impact,
            'confidence': self.confidence
        }

class AtlasWebSearchEngine:
    """Advanced web search engine for financial market research"""
    
    def __init__(self):
        self.logger = logger
        self.session = None
        
        # Financial news sources (RSS feeds and APIs)
        self.news_sources = {
            'yahoo_finance': 'https://feeds.finance.yahoo.com/rss/2.0/headline',
            'marketwatch': 'https://feeds.marketwatch.com/marketwatch/topstories/',
            'reuters_business': 'https://feeds.reuters.com/reuters/businessNews',
            'cnbc': 'https://search.cnbc.com/rs/search/combinedcms/view.xml?partnerId=wrss01&id=10000664',
            'bloomberg': 'https://feeds.bloomberg.com/markets/news.rss'
        }
        
        # Search engines and APIs
        self.search_engines = {
            'duckduckgo': 'https://api.duckduckgo.com/',
            'bing_news': 'https://api.bing.microsoft.com/v7.0/news/search',
            'google_news': 'https://news.google.com/rss/search'
        }
        
        # Symbol extraction patterns
        self.symbol_patterns = [
            r'\b[A-Z]{1,5}\b',  # Basic ticker symbols
            r'\$[A-Z]{1,5}\b',  # Symbols with $ prefix
            r'\b[A-Z]{1,5}:[A-Z]{1,5}\b'  # Exchange:Symbol format
        ]
    
    async def initialize(self):
        """Initialize the search engine"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'A.T.L.A.S. Financial Research Bot 1.0'
            }
        )
        self.logger.info("🔍 Web Search Engine initialized")
    
    async def close(self):
        """Close the search engine"""
        if self.session:
            await self.session.close()
    
    async def search_financial_news(self, query: str, symbol: Optional[str] = None, 
                                  limit: int = 10) -> List[SearchResult]:
        """Search for financial news and market information"""
        try:
            if not self.session:
                await self.initialize()
            
            # Enhance query with financial context
            enhanced_query = self._enhance_financial_query(query, symbol)
            
            # Search multiple sources
            all_results = []
            
            # Search RSS feeds
            rss_results = await self._search_rss_feeds(enhanced_query, symbol)
            all_results.extend(rss_results)
            
            # Search DuckDuckGo (no API key required)
            ddg_results = await self._search_duckduckgo(enhanced_query)
            all_results.extend(ddg_results)
            
            # Search Google News RSS
            google_results = await self._search_google_news_rss(enhanced_query)
            all_results.extend(google_results)
            
            # Filter and rank results
            filtered_results = self._filter_and_rank_results(all_results, symbol)
            
            return filtered_results[:limit]
            
        except Exception as e:
            self.logger.error(f"Error searching financial news: {e}")
            return []
    
    async def analyze_symbol_news(self, symbol: str) -> NewsAnalysis:
        """Comprehensive news analysis for a specific symbol"""
        try:
            # Search for recent news
            news_results = await self.search_financial_news(
                f"{symbol} stock news earnings", symbol, limit=20
            )
            
            # Extract key information
            key_headlines = [result.title for result in news_results[:5]]
            
            # Calculate overall sentiment
            sentiment_scores = [self._calculate_sentiment_score(result.snippet) 
                              for result in news_results]
            overall_sentiment_score = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0
            
            # Determine overall sentiment
            if overall_sentiment_score > 0.2:
                overall_sentiment = 'positive'
                market_impact = 'bullish'
            elif overall_sentiment_score < -0.2:
                overall_sentiment = 'negative'
                market_impact = 'bearish'
            else:
                overall_sentiment = 'neutral'
                market_impact = 'neutral'
            
            # Look for earnings information
            earnings_info = await self._extract_earnings_info(news_results, symbol)
            
            # Extract analyst ratings mentions
            analyst_ratings = self._extract_analyst_ratings(news_results)
            
            # Calculate confidence based on news volume and recency
            confidence = self._calculate_news_confidence(news_results)
            
            return NewsAnalysis(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                sentiment_score=overall_sentiment_score,
                key_headlines=key_headlines,
                recent_news=news_results,
                earnings_info=earnings_info,
                analyst_ratings=analyst_ratings,
                market_impact=market_impact,
                confidence=confidence
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing news for {symbol}: {e}")
            return NewsAnalysis(
                symbol=symbol,
                overall_sentiment='neutral',
                sentiment_score=0.0,
                key_headlines=[],
                recent_news=[],
                earnings_info=None,
                analyst_ratings=[],
                market_impact='neutral',
                confidence=0.0
            )
    
    def _enhance_financial_query(self, query: str, symbol: Optional[str] = None) -> str:
        """Enhance search query with financial context"""
        financial_terms = ['stock', 'trading', 'market', 'earnings', 'financial']
        
        # Add symbol if provided
        if symbol:
            query = f"{symbol} {query}"
        
        # Add financial context if not present
        if not any(term in query.lower() for term in financial_terms):
            query += " stock market financial"
        
        return query
    
    async def _search_rss_feeds(self, query: str, symbol: Optional[str] = None) -> List[SearchResult]:
        """Search financial RSS feeds"""
        results = []
        
        for source_name, feed_url in self.news_sources.items():
            try:
                async with self.session.get(feed_url) as response:
                    if response.status == 200:
                        content = await response.text()
                        feed = feedparser.parse(content)
                        
                        for entry in feed.entries[:5]:  # Limit per source
                            # Check relevance to query/symbol
                            if self._is_relevant_to_query(entry, query, symbol):
                                result = SearchResult(
                                    title=entry.get('title', ''),
                                    url=entry.get('link', ''),
                                    snippet=entry.get('summary', '')[:200],
                                    source=source_name,
                                    timestamp=self._parse_entry_date(entry),
                                    relevance_score=self._calculate_relevance(entry, query, symbol),
                                    sentiment=self._analyze_sentiment(entry.get('summary', '')),
                                    symbols_mentioned=self._extract_symbols(entry.get('title', '') + ' ' + entry.get('summary', ''))
                                )
                                results.append(result)
                                
            except Exception as e:
                self.logger.warning(f"Error fetching RSS feed {source_name}: {e}")
                continue
        
        return results
    
    async def _search_duckduckgo(self, query: str) -> List[SearchResult]:
        """Search DuckDuckGo for financial information"""
        results = []
        
        try:
            # DuckDuckGo Instant Answer API
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            async with self.session.get(self.search_engines['duckduckgo'], params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Process instant answer
                    if data.get('AbstractText'):
                        result = SearchResult(
                            title=data.get('Heading', 'DuckDuckGo Result'),
                            url=data.get('AbstractURL', ''),
                            snippet=data.get('AbstractText', '')[:200],
                            source='duckduckgo',
                            timestamp=datetime.now(),
                            relevance_score=0.8,
                            sentiment=self._analyze_sentiment(data.get('AbstractText', '')),
                            symbols_mentioned=self._extract_symbols(data.get('AbstractText', ''))
                        )
                        results.append(result)
                    
                    # Process related topics
                    for topic in data.get('RelatedTopics', [])[:3]:
                        if isinstance(topic, dict) and topic.get('Text'):
                            result = SearchResult(
                                title=topic.get('Text', '')[:100],
                                url=topic.get('FirstURL', ''),
                                snippet=topic.get('Text', '')[:200],
                                source='duckduckgo_related',
                                timestamp=datetime.now(),
                                relevance_score=0.6,
                                sentiment=self._analyze_sentiment(topic.get('Text', '')),
                                symbols_mentioned=self._extract_symbols(topic.get('Text', ''))
                            )
                            results.append(result)
                            
        except Exception as e:
            self.logger.warning(f"Error searching DuckDuckGo: {e}")
        
        return results
    
    async def _search_google_news_rss(self, query: str) -> List[SearchResult]:
        """Search Google News RSS feed"""
        results = []
        
        try:
            # Google News RSS search
            encoded_query = quote_plus(query)
            rss_url = f"https://news.google.com/rss/search?q={encoded_query}&hl=en-US&gl=US&ceid=US:en"
            
            async with self.session.get(rss_url) as response:
                if response.status == 200:
                    content = await response.text()
                    feed = feedparser.parse(content)
                    
                    for entry in feed.entries[:5]:
                        result = SearchResult(
                            title=entry.get('title', ''),
                            url=entry.get('link', ''),
                            snippet=entry.get('summary', '')[:200] if entry.get('summary') else entry.get('title', '')[:200],
                            source='google_news',
                            timestamp=self._parse_entry_date(entry),
                            relevance_score=0.7,
                            sentiment=self._analyze_sentiment(entry.get('title', '') + ' ' + entry.get('summary', '')),
                            symbols_mentioned=self._extract_symbols(entry.get('title', '') + ' ' + entry.get('summary', ''))
                        )
                        results.append(result)
                        
        except Exception as e:
            self.logger.warning(f"Error searching Google News: {e}")
        
        return results
    
    def _is_relevant_to_query(self, entry: Dict, query: str, symbol: Optional[str] = None) -> bool:
        """Check if RSS entry is relevant to the search query"""
        text = (entry.get('title', '') + ' ' + entry.get('summary', '')).lower()
        query_terms = query.lower().split()
        
        # Check for query terms
        query_matches = sum(1 for term in query_terms if term in text)
        
        # Check for symbol if provided
        symbol_match = symbol and symbol.upper() in text.upper() if symbol else True
        
        return query_matches >= len(query_terms) // 2 and symbol_match
    
    def _calculate_relevance(self, entry: Dict, query: str, symbol: Optional[str] = None) -> float:
        """Calculate relevance score for search result"""
        text = (entry.get('title', '') + ' ' + entry.get('summary', '')).lower()
        query_terms = query.lower().split()
        
        # Base relevance from query term matches
        query_matches = sum(1 for term in query_terms if term in text)
        base_score = query_matches / len(query_terms) if query_terms else 0.0
        
        # Bonus for symbol match
        symbol_bonus = 0.3 if symbol and symbol.upper() in text.upper() else 0.0
        
        # Bonus for financial keywords
        financial_keywords = ['earnings', 'revenue', 'profit', 'analyst', 'upgrade', 'downgrade', 'target']
        financial_bonus = sum(0.1 for keyword in financial_keywords if keyword in text)
        
        return min(1.0, base_score + symbol_bonus + financial_bonus)
    
    def _analyze_sentiment(self, text: str) -> str:
        """Simple sentiment analysis for financial text"""
        positive_words = ['up', 'gain', 'rise', 'bull', 'positive', 'upgrade', 'beat', 'strong', 'growth']
        negative_words = ['down', 'fall', 'bear', 'negative', 'downgrade', 'miss', 'weak', 'decline', 'loss']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _calculate_sentiment_score(self, text: str) -> float:
        """Calculate numerical sentiment score (-1.0 to 1.0)"""
        positive_words = ['up', 'gain', 'rise', 'bull', 'positive', 'upgrade', 'beat', 'strong', 'growth']
        negative_words = ['down', 'fall', 'bear', 'negative', 'downgrade', 'miss', 'weak', 'decline', 'loss']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.0
        
        sentiment_score = (positive_count - negative_count) / max(total_words, 1)
        return max(-1.0, min(1.0, sentiment_score * 10))  # Scale and clamp
    
    def _extract_symbols(self, text: str) -> List[str]:
        """Extract stock symbols from text"""
        symbols = set()
        
        for pattern in self.symbol_patterns:
            matches = re.findall(pattern, text.upper())
            symbols.update(matches)
        
        # Filter out common false positives
        false_positives = {'THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BUT', 'HAS'}
        symbols = {s.replace('$', '') for s in symbols if s not in false_positives and len(s) <= 5}
        
        return list(symbols)
    
    def _parse_entry_date(self, entry: Dict) -> datetime:
        """Parse date from RSS entry"""
        try:
            if 'published_parsed' in entry:
                return datetime(*entry.published_parsed[:6])
            elif 'updated_parsed' in entry:
                return datetime(*entry.updated_parsed[:6])
            else:
                return datetime.now()
        except:
            return datetime.now()
    
    def _filter_and_rank_results(self, results: List[SearchResult], symbol: Optional[str] = None) -> List[SearchResult]:
        """Filter and rank search results by relevance"""
        # Remove duplicates based on title similarity
        unique_results = []
        seen_titles = set()
        
        for result in results:
            title_key = result.title.lower()[:50]  # First 50 chars for similarity
            if title_key not in seen_titles:
                seen_titles.add(title_key)
                unique_results.append(result)
        
        # Sort by relevance score and recency
        unique_results.sort(key=lambda x: (x.relevance_score, x.timestamp), reverse=True)
        
        return unique_results
    
    async def _extract_earnings_info(self, news_results: List[SearchResult], symbol: str) -> Optional[Dict[str, Any]]:
        """Extract earnings information from news results"""
        earnings_keywords = ['earnings', 'eps', 'revenue', 'quarterly', 'q1', 'q2', 'q3', 'q4']
        
        for result in news_results:
            text = (result.title + ' ' + result.snippet).lower()
            if any(keyword in text for keyword in earnings_keywords):
                return {
                    'headline': result.title,
                    'date': result.timestamp.isoformat(),
                    'source': result.source,
                    'url': result.url,
                    'summary': result.snippet
                }
        
        return None
    
    def _extract_analyst_ratings(self, news_results: List[SearchResult]) -> List[Dict[str, Any]]:
        """Extract analyst ratings from news results"""
        rating_keywords = ['upgrade', 'downgrade', 'rating', 'target', 'analyst', 'price target']
        ratings = []
        
        for result in news_results:
            text = (result.title + ' ' + result.snippet).lower()
            if any(keyword in text for keyword in rating_keywords):
                ratings.append({
                    'headline': result.title,
                    'source': result.source,
                    'date': result.timestamp.isoformat(),
                    'url': result.url
                })
        
        return ratings[:3]  # Limit to 3 most recent
    
    def _calculate_news_confidence(self, news_results: List[SearchResult]) -> float:
        """Calculate confidence score based on news volume and recency"""
        if not news_results:
            return 0.0
        
        # Base confidence from number of results
        volume_score = min(len(news_results) / 10, 1.0)
        
        # Recency bonus (more recent = higher confidence)
        now = datetime.now()
        recency_scores = []
        for result in news_results:
            hours_old = (now - result.timestamp).total_seconds() / 3600
            recency_score = max(0, 1 - (hours_old / 168))  # Decay over 1 week
            recency_scores.append(recency_score)
        
        avg_recency = sum(recency_scores) / len(recency_scores) if recency_scores else 0.0
        
        # Source diversity bonus
        unique_sources = len(set(result.source for result in news_results))
        diversity_score = min(unique_sources / 5, 1.0)
        
        # Combined confidence
        confidence = (volume_score * 0.4 + avg_recency * 0.4 + diversity_score * 0.2)
        
        return min(1.0, confidence)

# Test function
async def test_web_search():
    """Test the web search engine"""
    search_engine = AtlasWebSearchEngine()
    
    try:
        await search_engine.initialize()
        
        print("🔍 Testing Web Search Engine")
        print("=" * 50)
        
        # Test general financial news search
        print("\n1. Testing general financial news search...")
        results = await search_engine.search_financial_news("market news today", limit=5)
        print(f"Found {len(results)} results")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result.title[:60]}... ({result.source})")
        
        # Test symbol-specific analysis
        print("\n2. Testing symbol-specific news analysis...")
        analysis = await search_engine.analyze_symbol_news("AAPL")
        print(f"AAPL Analysis:")
        print(f"   Sentiment: {analysis.overall_sentiment} ({analysis.sentiment_score:.2f})")
        print(f"   Market Impact: {analysis.market_impact}")
        print(f"   Confidence: {analysis.confidence:.2f}")
        print(f"   Key Headlines: {len(analysis.key_headlines)}")
        
        print("\n✅ Web Search Engine test complete!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    finally:
        await search_engine.close()

if __name__ == "__main__":
    asyncio.run(test_web_search())
