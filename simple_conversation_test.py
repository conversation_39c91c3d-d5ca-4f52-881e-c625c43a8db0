#!/usr/bin/env python3
"""
Simple Conversation Test
Test the core conversational functionality
"""

import requests
import json
import time

def test_single_conversation():
    """Test a single conversation exchange"""
    base_url = "http://localhost:8080"
    
    print("🧪 Testing Single Conversation Exchange")
    print("=" * 50)
    
    # Test 1: Greeting (should use conversational persona)
    print("\n1. Testing Greeting...")
    try:
        response = requests.post(
            f"{base_url}/api/v1/chat",
            json={"message": "Hello", "session_id": "test-greeting"},
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            print(f"✅ Response received: {response_text[:150]}...")
            print(f"   Type: {data.get('type', 'unknown')}")
            print(f"   Confidence: {data.get('confidence', 0):.2f}")
            
            # Check if it's conversational (not 6-point format)
            is_conversational = not any(term in response_text.lower() for term in ['why this trade', 'win/loss probabilities'])
            print(f"   Conversational: {'✅' if is_conversational else '❌'}")
            
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Trading Analysis (should use guru persona)
    print("\n2. Testing Trading Analysis...")
    try:
        response = requests.post(
            f"{base_url}/api/v1/chat",
            json={"message": "Analyze AAPL for trading", "session_id": "test-trading"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            print(f"✅ Response received: {response_text[:150]}...")
            print(f"   Type: {data.get('type', 'unknown')}")
            print(f"   Confidence: {data.get('confidence', 0):.2f}")
            
            # Check if it's 6-point format
            has_6_point = any(term in response_text.lower() for term in ['why this trade', 'win/loss', 'money in', 'stop plan', 'confidence'])
            print(f"   6-Point Format: {'✅' if has_6_point else '❌'}")
            
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Help Request (should use conversational persona)
    print("\n3. Testing Help Request...")
    try:
        response = requests.post(
            f"{base_url}/api/v1/chat",
            json={"message": "What can you help me with?", "session_id": "test-help"},
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            print(f"✅ Response received: {response_text[:150]}...")
            print(f"   Type: {data.get('type', 'unknown')}")
            
            # Check if it mentions capabilities
            mentions_capabilities = any(term in response_text.lower() for term in ['analyze', 'trading', 'market', 'stock'])
            print(f"   Mentions Capabilities: {'✅' if mentions_capabilities else '❌'}")
            
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_context_awareness():
    """Test conversation context awareness"""
    base_url = "http://localhost:8080"
    session_id = "test-context"
    
    print("\n🔍 Testing Context Awareness")
    print("=" * 50)
    
    # First message
    print("\n1. First message: 'I'm interested in Apple stock'")
    try:
        response1 = requests.post(
            f"{base_url}/api/v1/chat",
            json={"message": "I'm interested in Apple stock", "session_id": session_id},
            timeout=20
        )
        
        if response1.status_code == 200:
            data1 = response1.json()
            print(f"✅ First response: {data1.get('response', '')[:100]}...")
            
            # Follow-up message
            print("\n2. Follow-up: 'What do you think about it?'")
            response2 = requests.post(
                f"{base_url}/api/v1/chat",
                json={"message": "What do you think about it?", "session_id": session_id},
                timeout=20
            )
            
            if response2.status_code == 200:
                data2 = response2.json()
                response2_text = data2.get('response', '')
                print(f"✅ Follow-up response: {response2_text[:100]}...")
                
                # Check if it references Apple/AAPL
                references_apple = any(term in response2_text.lower() for term in ['apple', 'aapl'])
                print(f"   References Apple: {'✅' if references_apple else '❌'}")
                
            else:
                print(f"❌ Follow-up HTTP {response2.status_code}")
        else:
            print(f"❌ First HTTP {response1.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_health_check():
    """Test system health"""
    base_url = "http://localhost:8080"
    
    print("\n🏥 Testing System Health")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/v1/health", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ System Status: {data.get('status', 'unknown')}")
            
            engines = data.get('engines', {})
            active_engines = sum(1 for status in engines.values() if status == 'active')
            total_engines = len(engines)
            print(f"✅ Engines: {active_engines}/{total_engines} active")
            
            if active_engines >= total_engines * 0.8:  # 80% or more active
                print("✅ System is healthy")
            else:
                print("⚠️ Some engines not active")
                
        else:
            print(f"❌ Health check failed: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ Health check error: {e}")

if __name__ == "__main__":
    test_health_check()
    test_single_conversation()
    test_context_awareness()
    
    print("\n" + "=" * 50)
    print("🎯 Simple Conversation Test Complete")
    print("Check the server logs for detailed processing information.")
