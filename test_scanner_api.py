#!/usr/bin/env python3
"""
Test the scanner API endpoints
"""
import requests
import json
import time

def test_scanner_endpoint(endpoint, data=None):
    """Test a scanner endpoint"""
    url = f"http://localhost:8081/api/scan/{endpoint}"
    
    try:
        print(f"\n🧪 Testing {endpoint}...")
        print(f"URL: {url}")
        
        if data:
            print(f"Data: {json.dumps(data, indent=2)}")
            response = requests.post(url, json=data, timeout=30)
        else:
            response = requests.get(url, timeout=30)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success! Found {len(result.get('signals', []))} signals")
            
            # Show first few results
            signals = result.get('signals', [])
            for i, signal in enumerate(signals[:3]):
                print(f"\n{i+1}. {signal.get('symbol', 'Unknown')}")
                print(f"   Algorithm: {signal.get('algorithm', 'N/A')}")
                print(f"   Signal: {signal.get('signal', 'N/A')}")
                print(f"   Confidence: {signal.get('confidence', 'N/A')}")
                if 'setup_details' in signal:
                    details = signal['setup_details']
                    if isinstance(details, dict):
                        for key, value in list(details.items())[:3]:
                            print(f"   {key}: {value}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    """Test various scanner endpoints"""
    
    print("🚀 Testing ATLAS Scanner API Endpoints")
    print("=" * 50)
    
    # Test different scanners
    scanners_to_test = [
        ("crypto_macd", {"limit": 3}),
        ("iron_condor", {"limit": 3}),
        ("ttm_squeeze", {"limit": 3}),
        ("rsi_oversold", {"limit": 3}),
        ("bollinger_squeeze", {"limit": 3})
    ]
    
    results = {}
    
    for scanner, data in scanners_to_test:
        success = test_scanner_endpoint(scanner, data)
        results[scanner] = success
        time.sleep(1)  # Small delay between requests
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    for scanner, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{scanner:20} {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")

if __name__ == "__main__":
    main()
