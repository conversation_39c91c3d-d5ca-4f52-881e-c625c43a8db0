#!/usr/bin/env python3
"""
Quick A.T.L.A.S. System Test
Tests core functionality to validate fixes
"""

import requests
import json
import time
from datetime import datetime

def test_endpoint(url, method="GET", data=None, timeout=10):
    """Test an endpoint and return result"""
    try:
        if method == "GET":
            response = requests.get(url, timeout=timeout)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=timeout)
        
        return {
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "data": response.json() if response.status_code == 200 else None,
            "error": response.text if response.status_code != 200 else None
        }
    except Exception as e:
        return {
            "status_code": 0,
            "success": False,
            "data": None,
            "error": str(e)
        }

def main():
    base_url = "http://localhost:8080"
    
    print("🚀 Quick A.T.L.A.S. System Test")
    print("=" * 50)
    
    # Test 1: Health Check
    print("\n1. Testing Health Endpoint...")
    health = test_endpoint(f"{base_url}/api/v1/health")
    print(f"   Status: {'✅ PASS' if health['success'] else '❌ FAIL'}")
    if health['success']:
        engines = health['data'].get('engines', {})
        active_engines = sum(1 for status in engines.values() if status == 'active')
        print(f"   Engines: {active_engines}/{len(engines)} active")
    else:
        print(f"   Error: {health['error']}")
    
    # Test 2: Chat Interface
    print("\n2. Testing Chat Interface...")
    chat_data = {
        "message": "Analyze AAPL for a potential trade",
        "session_id": "test-123"
    }
    chat = test_endpoint(f"{base_url}/api/v1/chat", "POST", chat_data, 30)
    print(f"   Status: {'✅ PASS' if chat['success'] else '❌ FAIL'}")
    if chat['success']:
        response_text = chat['data'].get('response', '')
        has_6_point = any(term in response_text.lower() for term in ['why this trade', 'win/loss', 'money in', 'stop plan', 'confidence'])
        has_branding = 'A.T.L.A.S' in response_text and 'Predicto' in response_text
        print(f"   6-Point Format: {'✅' if has_6_point else '❌'}")
        print(f"   Branding: {'✅' if has_branding else '❌'}")
        print(f"   Length: {len(response_text)} chars")
    else:
        print(f"   Error: {chat['error']}")
    
    # Test 3: Quote Endpoint
    print("\n3. Testing Quote Endpoint...")
    quote = test_endpoint(f"{base_url}/api/v1/quote/AAPL")
    print(f"   Status: {'✅ PASS' if quote['success'] else '❌ FAIL'}")
    if quote['success']:
        quote_data = quote['data']
        print(f"   Symbol: {quote_data.get('symbol', 'N/A')}")
        print(f"   Price: {quote_data.get('price', 'N/A')}")
    else:
        print(f"   Error: {quote['error']}")
    
    # Test 4: Market Scanner
    print("\n4. Testing Market Scanner...")
    scan = test_endpoint(f"{base_url}/api/v1/scan")
    print(f"   Status: {'✅ PASS' if scan['success'] else '❌ FAIL'}")
    if scan['success']:
        signals = scan['data'].get('signals', [])
        print(f"   Signals Found: {len(signals)}")
    else:
        print(f"   Error: {scan['error']}")
    
    # Test 5: Portfolio
    print("\n5. Testing Portfolio Endpoint...")
    portfolio = test_endpoint(f"{base_url}/api/v1/portfolio")
    print(f"   Status: {'✅ PASS' if portfolio['success'] else '❌ FAIL'}")
    if portfolio['success']:
        portfolio_data = portfolio['data']
        print(f"   Total Value: ${portfolio_data.get('total_value', 'N/A')}")
        print(f"   Positions: {len(portfolio_data.get('positions', []))}")
    else:
        print(f"   Error: {portfolio['error']}")
    
    # Test 6: Conversational Test
    print("\n6. Testing Conversational Response...")
    conv_data = {
        "message": "Hello, I'm new to trading",
        "session_id": "test-123"
    }
    conv = test_endpoint(f"{base_url}/api/v1/chat", "POST", conv_data, 20)
    print(f"   Status: {'✅ PASS' if conv['success'] else '❌ FAIL'}")
    if conv['success']:
        response_text = conv['data'].get('response', '')
        is_conversational = not any(term in response_text.lower() for term in ['why this trade', 'win/loss probabilities'])
        print(f"   Conversational: {'✅' if is_conversational else '❌'}")
        print(f"   Length: {len(response_text)} chars")
    else:
        print(f"   Error: {conv['error']}")
    
    print("\n" + "=" * 50)
    print("🎯 Quick Test Complete")
    print("For detailed testing, run: python atlas_comprehensive_system_test.py")

if __name__ == "__main__":
    main()
