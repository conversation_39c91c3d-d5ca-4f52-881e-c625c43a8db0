#!/usr/bin/env python3
"""
A.T.L.A.S. Web Interface Synchronization Test
Test to verify that web interface and terminal API responses are consistent
"""

import requests
import json
import time
from datetime import datetime

def test_api_endpoint(test_name, request_data, expected_format="6-point"):
    """Test an API endpoint and validate the response"""
    print(f"\n🧪 Testing: {test_name}")
    print(f"📤 Request: {json.dumps(request_data, indent=2)}")
    
    try:
        response = requests.post(
            "http://localhost:8080/api/v1/chat",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📥 Response Type: {data.get('type', 'unknown')}")
            print(f"🎯 Confidence: {data.get('confidence', 0)}")
            
            # Check for 6-point format indicators
            response_text = data.get('response', '')
            has_6_point_format = (
                "1. **Why This Trade?**" in response_text and
                "2. **Win/Loss Probabilities**" in response_text and
                "3. **Potential Money In or Out**" in response_text and
                "4. **Smart Stop Plans**" in response_text and
                "5. **Market Context**" in response_text and
                "6. **Confidence Score**" in response_text
            )
            
            if has_6_point_format:
                print("✅ 6-Point Stock Market God Format: PRESENT")
            else:
                print("❌ 6-Point Stock Market God Format: MISSING")
                print(f"📝 Response Preview: {response_text[:200]}...")
            
            return {
                "success": True,
                "has_6_point": has_6_point_format,
                "response_type": data.get('type'),
                "confidence": data.get('confidence'),
                "response_length": len(response_text)
            }
        else:
            print(f"❌ Status: {response.status_code}")
            print(f"📝 Error: {response.text}")
            return {"success": False, "error": response.text}
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return {"success": False, "error": str(e)}

def main():
    """Run comprehensive web interface synchronization tests"""
    print("🚀 A.T.L.A.S. Web Interface Synchronization Test")
    print("=" * 60)
    
    # Test 1: Terminal-style request (minimal)
    terminal_request = {
        "message": "analyze AAPL stock",
        "session_id": "terminal_test"
    }
    
    # Test 2: Web interface-style request (with context)
    web_request = {
        "message": "analyze AAPL stock", 
        "session_id": "web_test",
        "context": {
            "panel_type": "left",
            "interface_type": "general_trading",
            "conversation_history": [],
            "interface": "web",
            "user_id": "web_user"
        }
    }
    
    # Test 3: Different stock symbol
    different_stock_request = {
        "message": "analyze MSFT stock",
        "session_id": "msft_test"
    }
    
    # Test 4: Scanner request
    scanner_request = {
        "message": "scan for TTM squeeze signals",
        "session_id": "scanner_test"
    }
    
    # Run tests
    results = []
    
    results.append(test_api_endpoint("Terminal-style Request (AAPL)", terminal_request))
    time.sleep(1)
    
    results.append(test_api_endpoint("Web Interface Request (AAPL)", web_request))
    time.sleep(1)
    
    results.append(test_api_endpoint("Different Stock (MSFT)", different_stock_request))
    time.sleep(1)
    
    results.append(test_api_endpoint("Scanner Request", scanner_request))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = sum(1 for r in results if r.get("success", False))
    tests_with_6_point = sum(1 for r in results if r.get("has_6_point", False))
    
    print(f"✅ Successful API calls: {successful_tests}/{len(results)}")
    print(f"🎯 6-Point format responses: {tests_with_6_point}/{len(results)}")
    
    if successful_tests == len(results):
        print("🎉 ALL TESTS PASSED - Web interface synchronization is working!")
    else:
        print("⚠️  Some tests failed - check the details above")
    
    if tests_with_6_point >= 3:  # Scanner might not use 6-point format
        print("✅ 6-Point Stock Market God format is working correctly!")
    else:
        print("❌ 6-Point format issues detected")
    
    print(f"\n🕒 Test completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
