"""
TTM Squeeze Scanner Service
Automated background scanner for TTM Squeeze patterns across S&P 500 and large cap stocks
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Set
import threading
import os
from pathlib import Path

from strategies import scan_ttm_squeeze, get_sp500_symbols
from comprehensive_api import ComprehensiveAPI

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ttm_scanner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TTMScannerService:
    """Automated TTM Squeeze scanner service"""
    
    def __init__(self, scan_interval_minutes: int = 10):
        self.scan_interval = scan_interval_minutes * 60  # Convert to seconds
        self.api = ComprehensiveAPI()
        self.running = False
        self.last_scan_time = None
        self.scan_results = []
        self.alerts = []
        self.scan_history = {}  # Track when we last alerted for each symbol
        self.alert_cooldown = 3600  # 1 hour cooldown before re-alerting same symbol
        
        # Create results directory
        self.results_dir = Path("ttm_scan_results")
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info(f"TTM Scanner Service initialized with {scan_interval_minutes} minute interval")
    
    def get_large_cap_symbols(self) -> List[str]:
        """Get all stocks with market cap > $100B"""
        try:
            # Get S&P 500 companies with market cap data
            sp500_data = self.api.fmp_sp500_constituent()
            
            large_caps = []
            if isinstance(sp500_data, list):
                for company in sp500_data:
                    symbol = company.get('symbol')
                    if not symbol:
                        continue
                    
                    # Get company profile for market cap
                    profile = self.api.fmp_profile(symbol)
                    if isinstance(profile, list) and profile:
                        market_cap = profile[0].get('mktCap', 0)
                        # Check if market cap > $100B
                        if market_cap > 100_000_000_000:
                            large_caps.append(symbol)
                            logger.debug(f"Added {symbol} with market cap ${market_cap/1e9:.1f}B")
            
            # Add some known mega-caps that might not be in S&P 500
            additional_mega_caps = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 
                                   'BRK.B', 'V', 'JNJ', 'WMT', 'JPM', 'PG', 'MA', 'UNH',
                                   'HD', 'DIS', 'BAC', 'ADBE', 'CRM', 'NFLX', 'PFE', 'TMO',
                                   'ABBV', 'KO', 'PEP', 'AVGO', 'CSCO', 'ACN', 'COST', 'MRK']
            
            for symbol in additional_mega_caps:
                if symbol not in large_caps:
                    large_caps.append(symbol)
            
            logger.info(f"Found {len(large_caps)} stocks with market cap > $100B")
            return large_caps
            
        except Exception as e:
            logger.error(f"Error getting large cap symbols: {e}")
            # Return default list if API fails
            return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA']
    
    def get_all_scan_symbols(self) -> List[str]:
        """Get combined list of S&P 500 and $100B+ market cap stocks"""
        # Get S&P 500 symbols
        sp500_symbols = get_sp500_symbols()
        
        # Get large cap symbols
        large_cap_symbols = self.get_large_cap_symbols()
        
        # Combine and deduplicate
        all_symbols = list(set(sp500_symbols + large_cap_symbols))
        
        logger.info(f"Total symbols to scan: {len(all_symbols)} (S&P 500 + $100B+ market caps)")
        return all_symbols
    
    def should_alert(self, symbol: str, timeframe: str) -> bool:
        """Check if we should alert for this symbol/timeframe combo"""
        key = f"{symbol}_{timeframe}"
        now = time.time()
        
        if key in self.scan_history:
            last_alert_time = self.scan_history[key]
            if now - last_alert_time < self.alert_cooldown:
                return False
        
        self.scan_history[key] = now
        return True
    
    def save_results(self, results: List[Dict]):
        """Save scan results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.results_dir / f"ttm_scan_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'total_signals': len(results),
                'results': results
            }, f, indent=2)
        
        # Also save latest results
        latest_file = self.results_dir / "latest_results.json"
        with open(latest_file, 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'total_signals': len(results),
                'results': results
            }, f, indent=2)
    
    def format_alert(self, signal: Dict) -> str:
        """Format a signal for alerting"""
        return (
            f"🎯 TTM SQUEEZE ALERT - {signal['symbol']} ({signal['timeframe']})\n"
            f"Pattern: {signal['setup_details']['pattern']}\n"
            f"Entry: ${signal['entry_price']}\n"
            f"Stop Loss: ${signal['stop_loss']}\n"
            f"Target 1: ${signal['target_1']}\n"
            f"Target 2: ${signal['target_2']}\n"
            f"EMA Trend: {signal['setup_details']['ema_trend']}\n"
            f"Confidence: {signal['confidence']}\n"
            f"Risk/Reward: 1:{signal['setup_details']['risk_reward_1']}"
        )
    
    async def run_single_scan(self):
        """Run a single scan iteration"""
        try:
            logger.info("Starting TTM Squeeze scan...")
            start_time = time.time()
            
            # Get all symbols to scan
            symbols = self.get_all_scan_symbols()
            
            # Split into batches for better performance
            batch_size = 50
            all_results = []
            
            for i in range(0, len(symbols), batch_size):
                batch = symbols[i:i + batch_size]
                logger.info(f"Scanning batch {i//batch_size + 1}/{(len(symbols) + batch_size - 1)//batch_size}")
                
                # Scan with multiple timeframes
                results = scan_ttm_squeeze(
                    symbols=batch,
                    timeframes=['5Min', '15Min', '30Min', '1Hour', '1Day'],
                    limit=1000  # No limit within batch
                )
                
                all_results.extend(results)
                
                # Small delay between batches to avoid overwhelming API
                await asyncio.sleep(1)
            
            # Filter for high confidence signals
            high_confidence_results = [r for r in all_results if r['confidence'] in ['High', 'Medium']]
            
            # Process new alerts
            new_alerts = []
            for result in high_confidence_results:
                if self.should_alert(result['symbol'], result['timeframe']):
                    new_alerts.append(result)
                    logger.info(self.format_alert(result))
            
            # Save results
            self.scan_results = high_confidence_results
            self.save_results(high_confidence_results)
            
            # Update scan time
            self.last_scan_time = datetime.now()
            
            scan_duration = time.time() - start_time
            logger.info(f"Scan completed in {scan_duration:.1f} seconds. Found {len(high_confidence_results)} signals, {len(new_alerts)} new alerts")
            
            return high_confidence_results
            
        except Exception as e:
            logger.error(f"Error during scan: {e}")
            return []
    
    async def scan_loop(self):
        """Main scanning loop"""
        self.running = True
        
        while self.running:
            try:
                # Run scan
                await self.run_single_scan()
                
                # Wait for next scan
                logger.info(f"Waiting {self.scan_interval/60:.0f} minutes until next scan...")
                await asyncio.sleep(self.scan_interval)
                
            except Exception as e:
                logger.error(f"Error in scan loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    def start(self):
        """Start the scanner service"""
        logger.info("Starting TTM Scanner Service...")
        asyncio.run(self.scan_loop())
    
    def stop(self):
        """Stop the scanner service"""
        logger.info("Stopping TTM Scanner Service...")
        self.running = False
    
    def get_latest_results(self) -> Dict:
        """Get the latest scan results"""
        latest_file = self.results_dir / "latest_results.json"
        if latest_file.exists():
            with open(latest_file, 'r') as f:
                return json.load(f)
        return {'results': [], 'timestamp': None}
    
    def get_scan_status(self) -> Dict:
        """Get current scanner status"""
        return {
            'running': self.running,
            'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'scan_interval_minutes': self.scan_interval / 60,
            'total_signals': len(self.scan_results),
            'next_scan_in': self.get_next_scan_time()
        }
    
    def get_next_scan_time(self) -> str:
        """Get time until next scan"""
        if not self.last_scan_time:
            return "Starting..."
        
        next_scan = self.last_scan_time + timedelta(seconds=self.scan_interval)
        time_until = (next_scan - datetime.now()).total_seconds()
        
        if time_until < 0:
            return "Running now..."
        
        minutes = int(time_until / 60)
        seconds = int(time_until % 60)
        return f"{minutes}m {seconds}s"


def run_scanner_service(interval_minutes: int = 10):
    """Run the scanner service"""
    scanner = TTMScannerService(scan_interval_minutes=interval_minutes)
    scanner.start()


if __name__ == "__main__":
    # Run with 10 minute interval by default
    run_scanner_service(10)
