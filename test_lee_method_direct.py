#!/usr/bin/env python3
"""
Direct test of Lee Method detection logic
"""

def test_lee_method_detection():
    """Test Lee Method detection directly"""
    print("🔧 TESTING LEE METHOD DETECTION LOGIC")
    print("=" * 60)
    
    # Test message
    message = "What are the 3 criteria for the Lee Method?"
    print(f"📝 Testing message: '{message}'")
    
    # Test the detection logic from the code
    lee_method_indicators = ['lee method', 'lee-method', 'leemethod']
    message_lower = message.lower()
    
    print(f"\n🔍 Detection Logic:")
    print(f"   Message (lowercase): '{message_lower}'")
    print(f"   Indicators: {lee_method_indicators}")
    
    # Check each indicator
    for indicator in lee_method_indicators:
        found = indicator in message_lower
        print(f"   '{indicator}' found: {'✅' if found else '❌'}")
    
    # Overall detection
    is_lee_method = any(indicator in message_lower for indicator in lee_method_indicators)
    print(f"\n🎯 Overall Lee Method Detection: {'✅ SUCCESS' if is_lee_method else '❌ FAILED'}")
    
    # Test symbol extraction prevention
    print(f"\n🔍 Symbol Extraction Test:")
    if is_lee_method:
        print("   ✅ Should return empty list (no symbols)")
        symbols = []  # This is what the code should do
    else:
        import re
        symbol_pattern = r'\b[A-Z]{1,5}\b'
        symbols = re.findall(symbol_pattern, message.upper())
    
    print(f"   Symbols extracted: {symbols}")
    
    if is_lee_method and len(symbols) == 0:
        print("   ✅ SUCCESS: No symbols extracted for Lee Method query")
    elif not is_lee_method and 'LEE' in symbols:
        print("   ❌ ISSUE: LEE would be extracted as symbol")
    else:
        print("   ⚠️ UNCLEAR: Unexpected result")
    
    # Test intent analysis
    print(f"\n🔍 Intent Analysis Test:")
    if is_lee_method:
        intent = {
            'symbols': [],
            'required_capabilities': ['lee_method_explanation'],
            'requires_stock_analysis': False,
            'requires_system_capability': True,
            'intent_type': 'lee_method_query'
        }
        print("   ✅ Should route to Lee Method handler")
        print(f"   Intent type: {intent['intent_type']}")
        print(f"   Requires stock analysis: {intent['requires_stock_analysis']}")
    else:
        print("   ❌ Would not route to Lee Method handler")
    
    return is_lee_method

if __name__ == "__main__":
    success = test_lee_method_detection()
    print(f"\n{'🎉 LEE METHOD DETECTION WORKING!' if success else '❌ LEE METHOD DETECTION NEEDS FIXING!'}")
