"""
ATLAS Clean Launcher
Starts ATLAS v2 with proper error handling
"""

import subprocess
import sys
import time
import os
import webbrowser
from pathlib import Path

def main():
    print("=" * 50)
    print("   🤖 ATLAS Trading Assistant v2")
    print("=" * 50)
    
    # Change to ATLAS_v2 directory
    atlas_dir = Path(__file__).parent
    os.chdir(atlas_dir)
    
    # Kill any existing Python processes on port 8000
    print("🧹 Cleaning up old processes...")
    try:
        if sys.platform == "win32":
            subprocess.run(["netstat", "-ano", "|", "findstr", ":8000"], shell=True, capture_output=True)
            subprocess.run(["taskkill", "/F", "/IM", "python.exe"], shell=True, capture_output=True)
        time.sleep(2)
    except:
        pass
    
    print("🚀 Starting ATLAS server...")
    
    # Start the server
    server_process = subprocess.Popen(
        [sys.executable, "atlas_web.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1
    )
    
    # Wait for server to start
    print("   Waiting for server to initialize...")
    time.sleep(3)
    
    # Check if server started successfully
    server_started = False
    for i in range(10):
        try:
            import requests
            response = requests.get("http://localhost:8000/api/health", timeout=1)
            if response.status_code == 200:
                server_started = True
                break
        except:
            pass
        time.sleep(1)
        print(f"   Checking server... ({i+1}/10)")
    
    if server_started:
        print("✅ Server started successfully!")
        print("\n📊 Opening ATLAS in your browser...")
        webbrowser.open("http://localhost:8000")
        
        print("\n" + "=" * 50)
        print("ATLAS is running at: http://localhost:8000")
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Keep the server running
        try:
            while True:
                line = server_process.stdout.readline()
                if line:
                    print(f"[SERVER] {line.strip()}")
                    
                # Check if process is still running
                if server_process.poll() is not None:
                    print("\n❌ Server stopped unexpectedly")
                    stderr = server_process.stderr.read()
                    if stderr:
                        print(f"Error: {stderr}")
                    break
                    
        except KeyboardInterrupt:
            print("\n\n🛑 Shutting down ATLAS...")
            server_process.terminate()
            time.sleep(1)
            print("✅ ATLAS stopped successfully")
    else:
        print("❌ Failed to start server")
        stderr = server_process.stderr.read()
        if stderr:
            print(f"Error: {stderr}")
        
if __name__ == "__main__":
    main() 