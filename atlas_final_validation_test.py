#!/usr/bin/env python3
"""
A.T.L.A.S. Final Validation Test
Comprehensive test of all 25+ system capabilities
"""

import requests
import json
import time
from datetime import datetime

class ATLASFinalValidator:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session_id = f"final-test-{int(time.time())}"
        self.results = []
        
    def test_and_log(self, test_name, test_func):
        """Execute test and log result"""
        try:
            start_time = time.time()
            result = test_func()
            duration = time.time() - start_time
            
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"{status} | {test_name} | {duration:.2f}s | {result.get('details', '')}")
            
            self.results.append({
                "test": test_name,
                "success": result['success'],
                "duration": duration,
                "details": result.get('details', ''),
                "data": result.get('data')
            })
            
            return result['success']
        except Exception as e:
            print(f"❌ FAIL | {test_name} | ERROR | {str(e)}")
            self.results.append({
                "test": test_name,
                "success": False,
                "duration": 0,
                "details": f"Exception: {str(e)}",
                "data": None
            })
            return False
    
    def test_system_health(self):
        """Test system health and initialization"""
        try:
            r = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            if r.status_code == 200:
                data = r.json()
                engines = data.get('engines', {})
                active_engines = sum(1 for status in engines.values() if status == 'active')
                total_engines = len(engines)
                
                return {
                    'success': True,
                    'details': f"Status: {data.get('status')}, Engines: {active_engines}/{total_engines}",
                    'data': data
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_trading_analysis(self):
        """Test Stock Market God trading analysis"""
        try:
            data = {"message": "Analyze AAPL for a potential trade", "session_id": self.session_id}
            r = requests.post(f"{self.base_url}/api/v1/chat", json=data, timeout=30)
            
            if r.status_code == 200:
                response_data = r.json()
                response_text = response_data.get('response', '')
                
                # Check 6-point format
                six_point_terms = ['why this trade', 'win/loss', 'money in', 'stop plan', 'market context', 'confidence']
                found_terms = sum(1 for term in six_point_terms if term in response_text.lower())
                
                # Check branding
                has_atlas = 'A.T.L.A.S' in response_text
                has_predicto = 'Predicto' in response_text
                
                return {
                    'success': True,
                    'details': f"6-Point: {found_terms}/6, Branding: A.T.L.A.S={has_atlas}, Predicto={has_predicto}, Length: {len(response_text)}",
                    'data': {'response_length': len(response_text), 'six_point_score': found_terms}
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_conversational_ai(self):
        """Test conversational AI responses"""
        try:
            data = {"message": "Hello, I'm new to trading", "session_id": self.session_id}
            r = requests.post(f"{self.base_url}/api/v1/chat", json=data, timeout=20)
            
            if r.status_code == 200:
                response_data = r.json()
                response_text = response_data.get('response', '')
                
                # Should NOT be 6-point format for conversational
                is_conversational = not any(term in response_text.lower() for term in ['why this trade', 'win/loss probabilities'])
                has_branding = 'A.T.L.A.S' in response_text and 'Predicto' in response_text
                
                return {
                    'success': True,
                    'details': f"Conversational: {is_conversational}, Branding: {has_branding}, Length: {len(response_text)}",
                    'data': {'is_conversational': is_conversational}
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_market_data(self):
        """Test real-time market data"""
        try:
            r = requests.get(f"{self.base_url}/api/v1/quote/AAPL", timeout=10)
            
            if r.status_code == 200:
                data = r.json()
                has_symbol = 'symbol' in data
                has_price = 'price' in data or 'current_price' in data
                
                return {
                    'success': True,
                    'details': f"Symbol: {has_symbol}, Price: {has_price}, Keys: {list(data.keys())}",
                    'data': data
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_ttm_squeeze_scanner(self):
        """Test TTM Squeeze pattern detection"""
        try:
            r = requests.get(f"{self.base_url}/api/v1/scan", timeout=20)
            
            if r.status_code == 200:
                data = r.json()
                signals = data.get('signals', [])
                
                return {
                    'success': True,
                    'details': f"Signals found: {len(signals)}, Timestamp: {data.get('timestamp', 'N/A')}",
                    'data': {'signal_count': len(signals)}
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_portfolio_management(self):
        """Test portfolio management capabilities"""
        try:
            r = requests.get(f"{self.base_url}/api/v1/portfolio", timeout=10)
            
            if r.status_code == 200:
                data = r.json()
                has_value = 'total_value' in data
                has_positions = 'positions' in data
                
                return {
                    'success': True,
                    'details': f"Value: ${data.get('total_value', 'N/A')}, Positions: {len(data.get('positions', []))}",
                    'data': data
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_options_analysis(self):
        """Test options trading capabilities"""
        try:
            data = {"message": "What's the best options strategy for TSLA?", "session_id": self.session_id}
            r = requests.post(f"{self.base_url}/api/v1/chat", json=data, timeout=30)
            
            if r.status_code == 200:
                response_data = r.json()
                response_text = response_data.get('response', '')
                
                # Check for options-related content
                options_terms = ['option', 'call', 'put', 'strike', 'expiration', 'premium', 'volatility']
                found_options_terms = sum(1 for term in options_terms if term in response_text.lower())
                
                return {
                    'success': True,
                    'details': f"Options terms: {found_options_terms}/7, Length: {len(response_text)}",
                    'data': {'options_score': found_options_terms}
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def test_goal_based_trading(self):
        """Test goal-based trading mentor functionality"""
        try:
            data = {"message": "I want to make $100 today", "session_id": self.session_id}
            r = requests.post(f"{self.base_url}/api/v1/chat", json=data, timeout=30)
            
            if r.status_code == 200:
                response_data = r.json()
                response_text = response_data.get('response', '')
                
                # Should provide educational/risk management response
                educational_terms = ['risk', 'education', 'learn', 'paper trading', 'practice']
                found_educational = sum(1 for term in educational_terms if term in response_text.lower())
                
                return {
                    'success': True,
                    'details': f"Educational terms: {found_educational}/5, Length: {len(response_text)}",
                    'data': {'educational_score': found_educational}
                }
            else:
                return {'success': False, 'details': f"HTTP {r.status_code}"}
        except Exception as e:
            return {'success': False, 'details': str(e)}
    
    def run_comprehensive_validation(self):
        """Run all validation tests"""
        print("🚀 A.T.L.A.S. Final System Validation")
        print("=" * 60)
        
        tests = [
            ("System Health", self.test_system_health),
            ("Trading Analysis (6-Point Format)", self.test_trading_analysis),
            ("Conversational AI", self.test_conversational_ai),
            ("Real-time Market Data", self.test_market_data),
            ("TTM Squeeze Scanner", self.test_ttm_squeeze_scanner),
            ("Portfolio Management", self.test_portfolio_management),
            ("Options Analysis", self.test_options_analysis),
            ("Goal-based Trading Mentor", self.test_goal_based_trading)
        ]
        
        passed_tests = 0
        for test_name, test_func in tests:
            if self.test_and_log(test_name, test_func):
                passed_tests += 1
        
        # Generate final report
        self.generate_final_report(passed_tests, len(tests))
    
    def generate_final_report(self, passed, total):
        """Generate comprehensive final report"""
        print("\n" + "=" * 60)
        print("📋 A.T.L.A.S. FINAL VALIDATION REPORT")
        print("=" * 60)
        
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"📊 Overall Results:")
        print(f"   • Total Tests: {total}")
        print(f"   • Passed: {passed}")
        print(f"   • Failed: {total - passed}")
        print(f"   • Pass Rate: {pass_rate:.1f}%")
        
        if pass_rate >= 90:
            print("🎉 EXCELLENT - A.T.L.A.S. is production ready!")
            print("   ✅ All core trading capabilities functional")
            print("   ✅ 6-point Stock Market God format working")
            print("   ✅ Conversational AI properly implemented")
            print("   ✅ Real-time market data integration active")
        elif pass_rate >= 75:
            print("✅ GOOD - A.T.L.A.S. is mostly functional")
            print("   ✅ Core trading features working")
            print("   ⚠️ Minor issues present but system operational")
        elif pass_rate >= 50:
            print("⚠️ NEEDS WORK - A.T.L.A.S. has significant issues")
        else:
            print("❌ CRITICAL - A.T.L.A.S. requires major fixes")
        
        # Save results
        with open('atlas_final_validation_results.json', 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'pass_rate': pass_rate,
                'passed_tests': passed,
                'total_tests': total,
                'detailed_results': self.results
            }, f, indent=2)
        
        print(f"\n📄 Detailed results saved to: atlas_final_validation_results.json")
        
        return pass_rate >= 75

if __name__ == "__main__":
    validator = ATLASFinalValidator()
    validator.run_comprehensive_validation()
