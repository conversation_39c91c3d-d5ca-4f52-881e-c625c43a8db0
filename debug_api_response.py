#!/usr/bin/env python3
"""
Debug script to test A.T.L.A.S. API responses directly and compare with web interface
"""
import requests
import json
import time
from datetime import datetime

def test_api_directly():
    """Test the API endpoint directly to see actual responses"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "name": "Simple Greeting",
            "message": "hello",
            "session_id": "debug_test_1"
        },
        {
            "name": "Stock Analysis Request", 
            "message": "Analyze AAPL for a potential trade",
            "session_id": "debug_test_2"
        },
        {
            "name": "Help Request",
            "message": "What can you do?",
            "session_id": "debug_test_3"
        },
        {
            "name": "Goal-Based Trading",
            "message": "I want to make $200 today, what are my best options?",
            "session_id": "debug_test_4"
        }
    ]
    
    print("🔍 A.T.L.A.S. API Response Debug Test")
    print("=" * 60)
    print(f"Testing at: {datetime.now()}")
    print(f"Endpoint: {url}")
    print()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📝 Test {i}: {test_case['name']}")
        print(f"Message: '{test_case['message']}'")
        print("-" * 40)
        
        # Prepare the exact same payload as web interface
        payload = {
            "message": test_case["message"],
            "session_id": test_case["session_id"],
            "user_id": "debug_user",
            "context": {
                "interface": "debug",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        try:
            print(f"🚀 Sending request...")
            start_time = time.time()
            
            response = requests.post(url, json=payload, timeout=30)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            print(f"📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    print(f"✅ Response received successfully")
                    print(f"📄 Response type: {data.get('type', 'unknown')}")
                    print(f"🎯 Confidence: {data.get('confidence', 'N/A')}")
                    
                    response_text = data.get('response', '')
                    print(f"📏 Response length: {len(response_text)} characters")
                    
                    # Check for indicators of response source
                    indicators = {
                        "OpenAI API": "generated by AI" in response_text.lower() or len(response_text) > 200,
                        "Hardcoded": len(response_text) < 100 and any(phrase in response_text for phrase in [
                            "I'm A.T.L.A.S. powered by Predicto",
                            "Advanced Trading & Learning Analysis System",
                            "What would you like to explore today?"
                        ]),
                        "Trading God": any(phrase in response_text for phrase in [
                            "SPY:", "| ACTION:", "| CONFIDENCE:", "SYMBOL:"
                        ]),
                        "Sophisticated Format": any(phrase in response_text for phrase in [
                            "**1. Why This Trade?**", "**2. Win/Loss Probabilities**", "**6. Confidence Score**"
                        ])
                    }
                    
                    print("🔍 Response Analysis:")
                    for indicator, detected in indicators.items():
                        status = "✅" if detected else "❌"
                        print(f"  {status} {indicator}")
                    
                    # Show response preview
                    preview_length = 300
                    if len(response_text) > preview_length:
                        preview = response_text[:preview_length] + "..."
                    else:
                        preview = response_text
                    
                    print(f"\n💬 Response Preview:")
                    print(f"'{preview}'")
                    
                    # Show full context if available
                    if 'context' in data:
                        context = data['context']
                        print(f"\n🔧 Context Info:")
                        for key, value in context.items():
                            if isinstance(value, (str, int, float, bool)):
                                print(f"  {key}: {value}")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error: {e}")
                    print(f"Raw response: {response.text[:200]}...")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out (30s)")
        except requests.exceptions.ConnectionError:
            print("🔌 Connection error - is the server running?")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
        
        print("\n" + "="*60 + "\n")
        
        # Small delay between requests
        time.sleep(1)
    
    print("🏁 API Debug Test Completed!")

if __name__ == "__main__":
    test_api_directly()
