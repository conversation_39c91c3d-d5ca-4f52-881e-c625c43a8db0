import requests

# HARDCODED keys for testing
ALPACA_API_KEY = "PK3BXR0CYPJV08WH08X2"
ALPACA_API_SECRET = "FgxdKTYeLGKbU6o3rm0oz8DTA5F471PQJgPYFkgc"
ALPACA_API_BASE = "https://paper-api.alpaca.markets"

headers = {
    "APCA-API-KEY-ID": ALPACA_API_KEY,
    "APCA-API-SECRET-KEY": ALPACA_API_SECRET
}

url = f"{ALPACA_API_BASE}/v2/account"

try:
    response = requests.get(url, headers=headers, timeout=10)
    print(f"Status: {response.status_code}")
    print(response.text)
except Exception as e:
    print(f"Error: {e}") 