#!/usr/bin/env python3
"""
Simple test to check Lee Method detection
"""

def test_lee_method_detection():
    """Test Lee Method detection logic"""
    print("🔧 TESTING LEE METHOD DETECTION")
    print("=" * 50)
    
    # Test the detection logic directly
    test_messages = [
        "What are the 3 criteria for the Lee Method?",
        "Explain the Lee Method",
        "What is the Lee Method?",
        "Lee Method criteria"
    ]
    
    # Simple detection logic
    lee_method_indicators = ['lee method', 'lee-method', 'leemethod']
    
    for message in test_messages:
        print(f"\n📝 Testing: '{message}'")
        
        # Check if Lee Method is detected
        message_lower = message.lower()
        is_lee_method = any(indicator in message_lower for indicator in lee_method_indicators)
        
        if is_lee_method:
            print("   ✅ SUCCESS: Lee Method detected!")
        else:
            print("   ❌ ISSUE: Lee Method not detected")
        
        # Check if LEE would be extracted as symbol
        import re
        symbol_pattern = r'\b[A-Z]{1,5}\b'
        potential_symbols = re.findall(symbol_pattern, message.upper())
        
        if 'LEE' in potential_symbols:
            print(f"   ⚠️ WARNING: 'LEE' would be extracted as symbol: {potential_symbols}")
        else:
            print(f"   ✅ GOOD: No LEE symbol extraction: {potential_symbols}")
    
    print("\n" + "=" * 50)
    print("🎯 LEE METHOD DETECTION TEST COMPLETE!")

if __name__ == "__main__":
    test_lee_method_detection()
