<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATLAS Trading Assistant v2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .account-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 10px;
        }
        
        .account-stat {
            background: rgba(255,255,255,0.1);
            padding: 10px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .account-stat .label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .account-stat .value {
            font-size: 1.3em;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .sidebar {
            width: 250px;
            background: #1a1a1a;
            padding: 20px;
            overflow-y: auto;
        }
        
        .scanner-section {
            margin-bottom: 30px;
        }
        
        .scanner-section h3 {
            margin-bottom: 15px;
            color: #4CAF50;
            font-size: 1.1em;
        }
        
        .scanner-btn {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            background: #2a2a2a;
            border: 1px solid #3a3a3a;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .scanner-btn:hover {
            background: #3a3a3a;
            border-color: #4CAF50;
        }
        
        .scanner-status {
            background-color: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid #2a2a2a;
        }
        
        .scanner-status p {
            margin: 5px 0;
            font-size: 14px;
            color: #ccc;
        }
        
        .scanner-status span {
            font-weight: bold;
            color: white;
        }
        
        .scanner-btn.special {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            font-weight: bold;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f0f;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            animation: fadeIn 0.3s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message.user {
            background: #1e3c72;
            margin-left: auto;
        }
        
        .message.assistant {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
        }
        
        .message.scanner-result {
            background: #1a2a1a;
            border: 1px solid #2a4a2a;
            max-width: 95%;
        }
        
        .scanner-result-item {
            background: #0a1a0a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #4CAF50;
        }
        
        .scanner-result-item .symbol {
            font-size: 1.2em;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .scanner-result-item .details {
            margin-top: 5px;
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .chat-input {
            padding: 20px;
            background: #1a1a1a;
            border-top: 1px solid #2a2a2a;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        #messageInput {
            flex: 1;
            padding: 15px;
            background: #0a0a0a;
            border: 1px solid #2a2a2a;
            color: white;
            border-radius: 25px;
            font-size: 16px;
        }
        
        #messageInput:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        #sendButton {
            padding: 15px 30px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border: none;
            color: white;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        #sendButton:hover {
            transform: scale(1.05);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .positions-section {
            background: #1a1a1a;
            padding: 15px;
            margin: 20px;
            border-radius: 10px;
            border: 1px solid #2a2a2a;
        }
        
        .position-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background: #0a0a0a;
            border-radius: 5px;
        }
        
        .profit { color: #4CAF50; }
        .loss { color: #f44336; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 ATLAS Trading Assistant v2</h1>
        <div class="account-info" id="accountInfo">
            <div class="account-stat">
                <div class="label">Buying Power</div>
                <div class="value" id="buyingPower">Loading...</div>
            </div>
            <div class="account-stat">
                <div class="label">Portfolio Value</div>
                <div class="value" id="portfolioValue">Loading...</div>
            </div>
            <div class="account-stat">
                <div class="label">Day Trades</div>
                <div class="value" id="dayTrades">Loading...</div>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <div class="sidebar">
            <div class="scanner-section">
                <h3>🔥 Hot Scanners</h3>
                <button class="scanner-btn special" onclick="runScanner('ttm_squeeze')">
                    TTM Squeeze 🎯
                </button>
                <button class="scanner-btn" onclick="runScanner('insider_buying')">
                    Insider Buying 💼
                </button>
                <button class="scanner-btn" onclick="runScanner('volume_spike')">
                    Volume Spikes 📊
                </button>
            </div>
            
            <div class="scanner-section">
                <h3>📈 Stock Scanners</h3>
                <button class="scanner-btn" onclick="runScanner('ma_crossover')">
                    EMA Crossover
                </button>
                <button class="scanner-btn" onclick="runScanner('bollinger_reversion')">
                    Bollinger Reversion
                </button>
                <button class="scanner-btn" onclick="runScanner('donchian_breakout')">
                    Donchian Breakout
                </button>
                <button class="scanner-btn" onclick="runScanner('rsi_momentum')">
                    RSI Momentum
                </button>
            </div>
            
            <div class="scanner-section">
                <h3>🎯 Options Scanners</h3>
                <button class="scanner-btn" onclick="runScanner('iron_condor')">
                    Iron Condor
                </button>
                <button class="scanner-btn" onclick="runScanner('calendar_spread')">
                    Calendar Spread
                </button>
                <button class="scanner-btn" onclick="runScanner('cash_secured_put')">
                    Cash Secured Put
                </button>
            </div>
            
            <div class="scanner-section">
                <h3>🪙 Crypto Scanners</h3>
                <button class="scanner-btn" onclick="runScanner('crypto_macd')">
                    Crypto MACD
                </button>
                <button class="scanner-btn" onclick="runScanner('crypto_rsi')">
                    Crypto RSI
                </button>
            </div>
        </div>
        
        <!-- TTM Scanner Status -->
        <div class="scanner-section">
            <h3>🎯 TTM Squeeze Auto-Scanner</h3>
            <div id="scanner-status" class="scanner-status">
                <p>Status: <span id="scanner-status-text">Checking...</span></p>
                <p>Last Scan: <span id="scanner-last-scan">-</span></p>
                <p>Active Signals: <span id="scanner-signal-count">0</span></p>
                <button class="scanner-btn" onclick="checkScannerStatus()">Refresh Status</button>
                <button class="scanner-btn" onclick="viewScannerResults()">View Results</button>
            </div>
            <div id="scanner-results" style="margin-top: 20px;"></div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <strong>ATLAS:</strong> Welcome! I'm your AI trading assistant. I can help you:
                    <ul style="margin-top: 10px; margin-left: 20px;">
                        <li>Find profitable trades using advanced scanners</li>
                        <li>Analyze any stock or crypto</li>
                        <li>Execute trades with proper risk management</li>
                        <li>Scan for TTM Squeeze setups and other patterns</li>
                    </ul>
                    <br>
                    Try asking: "Find me a trade to make $50 today" or click any scanner button!
                </div>
            </div>
            
            <div class="chat-input">
                <div class="input-group">
                    <input type="text" id="messageInput" placeholder="Ask me anything about trading..." 
                           onkeypress="if(event.key === 'Enter') sendMessage()">
                    <button id="sendButton" onclick="sendMessage()">Send</button>
                </div>
            </div>
        </div>
    </div>
    
    <div id="squeeze-alerts-panel" style="position:fixed;top:10px;right:10px;width:350px;background:#222;color:#fff;padding:16px;border-radius:8px;z-index:1000;box-shadow:0 2px 8px #0008;">
        <h3 style="margin-top:0;">🚦 TTM Squeeze Alerts</h3>
        <table id="squeeze-alerts-table" style="width:100%;color:#fff;background:#222;font-size:14px;">
            <thead>
                <tr><th>Symbol</th><th>Time</th><th>Score</th><th>Close</th></tr>
            </thead>
            <tbody></tbody>
        </table>
        <div id="squeeze-alerts-empty" style="color:#aaa;display:none;">No new squeeze signals.</div>
    </div>
    
    <script>
        // Session ID for maintaining conversation context
        let sessionId = 'session-' + Date.now();
        
        // Load account info on startup
        async function loadAccountInfo() {
            try {
                const response = await fetch('/api/account');
                const data = await response.json();
                
                document.getElementById('buyingPower').textContent = 
                    `$${parseFloat(data.buying_power).toLocaleString()}`;
                document.getElementById('portfolioValue').textContent = 
                    `$${parseFloat(data.portfolio_value).toLocaleString()}`;
                document.getElementById('dayTrades').textContent = 
                    `${data.day_trade_count}/3`;
            } catch (error) {
                console.error('Error loading account:', error);
            }
        }
        
        // Send message to ATLAS
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            input.value = '';
            
            // Show loading
            const loadingId = addMessage('<div class="loading"></div>', 'assistant');
            
            try {
                // Send to ATLAS with session ID
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        message, 
                        session_id: sessionId 
                    })
                });
                
                const data = await response.json();
                
                // Update session ID if provided
                if (data.session_id) {
                    sessionId = data.session_id;
                }
                
                // Remove loading and add response
                document.getElementById(loadingId).remove();
                addMessage(data.response, 'assistant');
                
                // If response contains a trade proposal, highlight the confirm button
                if (data.response.includes('Do you confirm')) {
                    input.placeholder = "Type 'yes' to confirm the trade...";
                    input.focus();
                }
                
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage('Sorry, I encountered an error. Please try again.', 'assistant');
            }
        }
        
        // Run a scanner
        async function runScanner(scannerName) {
            // Show loading message
            const loadingId = addMessage(
                `<strong>Running ${scannerName.replace('_', ' ')} scanner...</strong><br>
                <div class="loading"></div>`, 
                'assistant'
            );
            
            try {
                const response = await fetch(`/api/scan/${scannerName}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        limit: 10
                    })
                });
                
                const data = await response.json();
                
                // Remove loading
                document.getElementById(loadingId).remove();
                
                // Display results
                displayScannerResults(scannerName, data.results);
                
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage(`Error running ${scannerName} scanner: ${error.message}`, 'assistant');
            }
        }
        
        // Display scanner results
        function displayScannerResults(scannerName, results) {
            if (!results || results.length === 0) {
                addMessage(`No signals found for ${scannerName.replace('_', ' ')} scanner.`, 'assistant');
                return;
            }
            
            let html = `<strong>🎯 ${scannerName.replace('_', ' ').toUpperCase()} Scanner Results:</strong><br><br>`;
            
            results.forEach((signal, index) => {
                // Handle different signal formats
                const symbol = signal.symbol || 'Unknown';
                const signalType = signal.signal || 'SIGNAL';
                const confidence = signal.confidence || 'N/A';
                const algorithm = signal.algorithm || signal.strategy_type || scannerName.replace('_', ' ');
                
                html += `
                    <div class="scanner-result-item">
                        <div class="symbol">${index + 1}. ${symbol}</div>
                        <div class="details">
                            ${algorithm}<br>
                            Signal: ${signalType}<br>
                            ${signal.entry_price ? `Entry: $${signal.entry_price.toFixed ? signal.entry_price.toFixed(2) : signal.entry_price}<br>` : ''}
                            Confidence: ${confidence}<br>
                            ${formatSetupDetails(signal.setup_details)}
                        </div>
                    </div>
                `;
            });
            
            const messageId = addMessage(html, 'scanner-result');
        }
        
        // Format setup details
        function formatSetupDetails(details) {
            if (!details || typeof details !== 'object') return '';
            
            let formatted = '';
            for (const [key, value] of Object.entries(details)) {
                if (value === undefined || value === null) continue;
                
                if (key === 'interpretation') {
                    formatted += `<strong>${value}</strong><br>`;
                } else if (typeof value === 'number') {
                    formatted += `${key.replace(/_/g, ' ')}: ${value.toFixed ? value.toFixed(2) : value}<br>`;
                } else if (typeof value === 'boolean') {
                    formatted += `${key.replace(/_/g, ' ')}: ${value ? 'Yes' : 'No'}<br>`;
                } else if (typeof value !== 'object') {
                    formatted += `${key.replace(/_/g, ' ')}: ${value}<br>`;
                }
            }
            return formatted;
        }
        
        // Add message to chat
        function addMessage(content, type) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const messageId = 'msg-' + Date.now();
            
            messageDiv.id = messageId;
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = type === 'user' ? 
                `<strong>You:</strong> ${content}` : 
                content.startsWith('<strong>') ? content : `<strong>ATLAS:</strong> ${content}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            return messageId;
        }
        
        // Load positions periodically
        async function loadPositions() {
            try {
                const response = await fetch('/api/positions');
                const positions = await response.json();
                
                // Update positions display if needed
                // This could be enhanced to show positions in the UI
                
            } catch (error) {
                console.error('Error loading positions:', error);
            }
        }
        
        // Initialize
        loadAccountInfo();
        setInterval(loadAccountInfo, 30000); // Update every 30 seconds
        setInterval(loadPositions, 10000); // Update positions every 10 seconds

        // Check scanner status on load
        checkScannerStatus();
        
        // Auto-refresh scanner status every 30 seconds
        setInterval(checkScannerStatus, 30000);
    </script>

    <script>
        async function checkScannerStatus() {
            try {
                const response = await fetch('/api/scanner/status');
                const data = await response.json();
                
                document.getElementById('scanner-status-text').textContent = data.status || 'Unknown';
                document.getElementById('scanner-last-scan').textContent = data.last_scan || '-';
                document.getElementById('scanner-signal-count').textContent = data.total_signals || '0';
                
                // Update status color
                const statusElement = document.getElementById('scanner-status-text');
                if (data.status === 'active') {
                    statusElement.style.color = '#4CAF50';
                } else {
                    statusElement.style.color = '#f44336';
                }
            } catch (error) {
                console.error('Error checking scanner status:', error);
            }
        }

        async function viewScannerResults() {
            try {
                const response = await fetch('/api/scanner/results');
                const data = await response.json();
                
                if (data.results && data.results.length > 0) {
                    let resultsHtml = '<h3>TTM Squeeze Signals Found:</h3>';
                    data.results.forEach(signal => {
                        resultsHtml += `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                                <strong>${signal.symbol} (${signal.timeframe})</strong> - ${signal.confidence} Confidence<br>
                                Pattern: ${signal.setup_details.pattern}<br>
                                Entry: $${signal.entry_price} | Stop: $${signal.stop_loss}<br>
                                Target 1: $${signal.target_1} | Target 2: $${signal.target_2}<br>
                                EMA Trend: ${signal.setup_details.ema_trend}<br>
                                Risk/Reward: 1:${signal.setup_details.risk_reward_1}
                            </div>
                        `;
                    });
                    
                    document.getElementById('scanner-results').innerHTML = resultsHtml;
                } else {
                    document.getElementById('scanner-results').innerHTML = '<p>No TTM Squeeze signals found in latest scan.</p>';
                }
            } catch (error) {
                console.error('Error fetching scanner results:', error);
                document.getElementById('scanner-results').innerHTML = '<p>Error loading results.</p>';
            }
        }
    </script>

    <script>
        async function fetchSqueezeAlerts() {
            try {
                const res = await fetch('/api/squeeze_alerts');
                const data = await res.json();
                const alerts = data.alerts || [];
                const tbody = document.querySelector('#squeeze-alerts-table tbody');
                tbody.innerHTML = '';
                if (alerts.length === 0) {
                    document.getElementById('squeeze-alerts-empty').style.display = '';
                    document.getElementById('squeeze-alerts-table').style.display = 'none';
                } else {
                    document.getElementById('squeeze-alerts-empty').style.display = 'none';
                    document.getElementById('squeeze-alerts-table').style.display = '';
                    alerts.forEach(a => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `<td><b>${a.symbol}</b></td><td>${a.time ? a.time.slice(0,16).replace('T',' ') : ''}</td><td>${a.score.toFixed(2)}</td><td>${a.close.toFixed(2)}</td>`;
                        tbody.appendChild(tr);
                    });
                }
            } catch (e) {
                // Optionally show error
            }
        }
        setInterval(fetchSqueezeAlerts, 10000);
        window.addEventListener('DOMContentLoaded', fetchSqueezeAlerts);
    </script>
</body>
</html> 